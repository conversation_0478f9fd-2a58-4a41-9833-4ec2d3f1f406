import {
  BedrockRuntimeClient,
  InvokeModelCommand,
} from '@aws-sdk/client-bedrock-runtime';
import { config } from './src/config';
import { CLAIM_APPROVAL_PROMPT } from './src/integrations/ai/prompts/claim-approval';

function extractJsonFromText(text: string): any | null {
  const jsonRegex = /```(?:json)?\s*([\s\S]*?)\s*```/;
  const match = text.match(jsonRegex);

  if (match && match[1]) {
    try {
      return JSON.parse(match[1]);
    } catch (e) {
      console.log(
        'Failed to parse JSON from code block, trying alternative methods',
      );
    }
  }

  // If no code blocks found or parsing failed, try to find JSON array/object patterns
  const jsonObjectRegex = /(\[[\s\S]*\]|\{[\s\S]*\})/;
  const objectMatch = text.match(jsonObjectRegex);

  if (objectMatch && objectMatch[1]) {
    try {
      return JSON.parse(objectMatch[1]);
    } catch (e) {
      console.log('Failed to parse <PERSON><PERSON><PERSON> from object pattern');
    }
  }

  return null;
}

export const claim2 = `{
    "patientAge": "63 Yrs",
    "patientGender": "Female",
    "diagnosis": [
        "Diabetes",
        "Acute Malaria"
    ],
    "visitationType": "Outpatient",
    "treatmentStartDate": "2025-02-27T19:41:02",
    "treatmentEndDate": "2025-02-27T19:41:02",
    "serviceProvided": [
        {
            "id": "1",
            "utilization": "GENERAL",
            "type": "Consultation",
            "quantity": 1
        },
        {
            "id": "2",
            "utilization": "DRUGS",
            "type": "Metformin 500mg",
            "quantity": 14
        },
        {
            "id": "3",
            "utilization": "CONSUMABLES",
            "type": "Needle And Syringe (All Sizes)",
            "quantity": 4
        },
        {
            "id": "4",
            "utilization": "LABORATORY",
            "type": "RBS/FBS",
            "quantity": 1
        }
    ]
}`;

export const claim3 = `{
    "patientAge": "63 Yrs",
    "patientGender": "Female",
    "diagnosis": [
        "Severe Malaria",
    ],
    "visitationType": "Inpatient",
    "treatmentStartDate": "2025-02-20T19:41:02",
    "treatmentEndDate": "2025-02-29T19:41:02",
    "serviceProvided": [
        {
            "id": "1",
            "utilization": "DRUGS",
            "type": "Artesunate 50mg Tab",
            "quantity": 3
        },
        {
            "id": "2",
            "utilization": "DRUGS",
            "type": "Paracetamol 500mg",
            "quantity": 2
        },
        {
            "id": "3",
            "utilization": "DRUGS",
            "type": "Panadol 500mg",
            "quantity": 2
        }
    ]
}`;
export async function processClaimWithBedrock(claim, claimNumber) {
  if (!config.ai.bedrockDeepseekModelId) {
    console.error(
      'No ModelID specified in environment variables. Please set AI_MODEL_ID to the ID of the Deepseek model you want to use.',
    );
    return;
  }
  console.log(`Processing Claim ${claimNumber} with Bedrock`);
  const startTime = process.hrtime();
  const client = new BedrockRuntimeClient({
    region: 'us-east-1',
  });
  try {
    const requestBody = {
      messages: [
        {
          role: 'system',
          content:
            CLAIM_APPROVAL_PROMPT() +
            // eslint-disable-next-line max-len
            '\n\nIMPORTANT: Respond with a clean, well-formatted JSON array. Do not include backticks, code formatting, or any other text. ONLY return the JSON array.',
        },
        { role: 'user', content: claim },
      ],
      max_tokens: 4096,
      temperature: 0.2,
    };

    // Create the command to invoke the model
    const command = new InvokeModelCommand({
      modelId: config.ai.bedrockDeepseekModelId,
      body: JSON.stringify(requestBody),
      contentType: 'application/json',
      accept: 'application/json',
    });

    // Send the request to Bedrock
    const response = await client.send(command);

    // Parse the response
    const responseBody = JSON.parse(new TextDecoder().decode(response.body));
    // console.log('Bedrock response:', JSON.stringify(responseBody, null, 2));

    let content;

    if (
      responseBody.choices &&
      responseBody.choices[0] &&
      responseBody.choices[0].message
    ) {
      content = responseBody.choices[0].message.content;
    } else if (responseBody.content) {
      content = responseBody.content;
    } else if (responseBody.completion) {
      content = responseBody.completion;
    } else if (responseBody.generation) {
      content = responseBody.generation;
    } else {
      console.log(
        'Unexpected response format. Full response:',
        JSON.stringify(responseBody, null, 2),
      );
      throw new Error('Unable to extract content from Bedrock response');
    }

    console.log('Extracted content:', content);

    try {
      const extractedJson = extractJsonFromText(content);
      if (extractedJson) {
        // console.log('Parsed JSON:', JSON.stringify(extractedJson, null, 2));
        return extractedJson;
      }

      const jsonContent = JSON.parse(content);
      console.log('Parsed JSON:', JSON.stringify(jsonContent, null, 2));
      return jsonContent;
    } catch (parseError) {
      console.error('JSON parsing error:', parseError);

      if (typeof content === 'object') {
        console.log('Content is already an object, returning directly');
        return content;
      }

      throw new Error(`Failed to parse response as JSON: ${content}`);
    }
  } catch (error) {
    console.error(`Error processing claim ${claimNumber} with Bedrock:`, error);
    return null;
  } finally {
    const endTime = process.hrtime(startTime);
    console.log(`Execution time: ${endTime[0]}s ${endTime[1] / 1000000}ms`);
  }
}

async function processClaim(claim, claimNumber) {
  console.log(`Processing Claim ${claimNumber}`);
  const startTime = process.hrtime();
  console.log(process.env.AI_API_BASE_URL);
  try {
    const response = await fetch(
      'https://openrouter.ai/api/v1/chat/completions',
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${process.env.AI_API_KEY}`,
          'HTTP-Referer': 'https://clinify.com', // Replace with your actual domain
          'X-Title': 'Clinify Claim Approval',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-chat',
          messages: [
            { role: 'system', content: CLAIM_APPROVAL_PROMPT() },
            { role: 'user', content: claim },
          ],
          response_format: {
            type: 'json_schema',
            json_schema: {
              name: 'claim_approval',
              schema: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    status: {
                      type: 'string',
                      enum: ['approved', 'rejected'],
                    },
                    reasons: { type: 'string' },
                    classification: { type: 'string' },
                  },
                  required: ['id', 'status', 'reasons', 'classification'],
                  additionalProperties: false,
                },
              },
              strict: true,
            },
          },
          // temperature: 0.2,
        }),
      },
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        `API error: ${response.status} - ${JSON.stringify(errorData)}`,
      );
    }

    const result = await response.json();
    // console.log(result);
    const content = result.choices[0].message.content;

    try {
      const jsonContent = JSON.parse(content);
      console.log('Parsed JSON:', JSON.stringify(jsonContent, null, 2));
      return jsonContent;
    } catch (parseError) {
      console.error('JSON parsing error:', parseError);
      throw new Error(`Failed to parse response as JSON: ${content}`);
    }
  } catch (error) {
    console.error(`Error processing claim ${claimNumber}:`, error.message);
    return null;
  } finally {
    const endTime = process.hrtime(startTime);
    console.log(`Execution time: ${endTime[0]}s ${endTime[1] / 1000000}ms`);
  }
}

async function main() {
  if (!process.env.AI_API_KEY) {
    console.error('Error: AI_API_KEY is not set in the environment variables.');
    return;
  }

  // Process claim1
  // await processClaim(claim1, 1);

  // Process claim2 with OpenRouter
  console.log('Processing with OpenRouter:');
  await processClaim(claim2, 2);

  // Process claim2 with Bedrock
  console.log('\nProcessing with AWS Bedrock:');
  await processClaimWithBedrock(claim2, 2);
}

main().catch((error) => {
  console.error('Main execution error:', error);
});
