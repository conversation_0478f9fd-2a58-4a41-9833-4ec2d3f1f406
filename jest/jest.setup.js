jest.setTimeout(300000);

jest.mock('mongoose/lib/drivers/node-mongodb-native/index.js', () => {
  return {
    Connection: jest.fn(),
    Collection: jest.fn(() => [{}]),
  };
});

jest.mock('jsdom', () => {
  return {
    JSDOM: jest.fn().mockImplementation(() => {
      return {
        window: jest.fn(() => ({})),
      };
    }),
  };
});

jest.mock('twilio', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    messages: {
      create: jest.fn(() => Promise.resolve({})),
    },
  })),
}));

jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn(),
  PutObjectCommand: jest.fn(),
  GetObjectCommand: jest.fn(),
  DeleteObjectCommand: jest.fn(),
  ListObjectsV2Command: jest.fn(),
  DeleteObjectsCommand: jest.fn(),
}));

jest.mock('@aws-sdk/client-transcribe', () => ({
  TranscribeClient: jest.fn(),
  StartMedicalScribeJobCommand: jest.fn(),
  GetMedicalScribeJobCommand: jest.fn(),
}));

jest.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: jest.fn(),
}));

jest.mock('ioredis', () => {
  const mockRedis = {
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    quit: jest.fn().mockResolvedValue(undefined),
    on: jest.fn(),
    off: jest.fn(),
    status: 'ready',
  };

  return {
    __esModule: true,
    default: jest.fn(() => mockRedis),
    Redis: jest.fn(() => mockRedis),
  };
});

jest.mock('bull', () => {
  const mockJob = {
    id: 'mock-job-id',
    data: {},
    progress: jest.fn(),
    log: jest.fn(),
    moveToCompleted: jest.fn(),
    moveToFailed: jest.fn(),
  };

  const mockQueue = {
    add: jest.fn().mockResolvedValue(mockJob),
    process: jest.fn(),
    close: jest.fn().mockResolvedValue(undefined),
    clean: jest.fn(),
    getJob: jest.fn().mockResolvedValue(mockJob),
    getJobs: jest.fn().mockResolvedValue([]),
    getWaiting: jest.fn().mockResolvedValue([]),
    getActive: jest.fn().mockResolvedValue([]),
    getCompleted: jest.fn().mockResolvedValue([]),
    getFailed: jest.fn().mockResolvedValue([]),
    pause: jest.fn().mockResolvedValue(undefined),
    resume: jest.fn().mockResolvedValue(undefined),
    count: jest.fn().mockResolvedValue(0),
    empty: jest.fn().mockResolvedValue(undefined),
    on: jest.fn(),
    off: jest.fn(),
  };

  return jest.fn(() => mockQueue);
});
