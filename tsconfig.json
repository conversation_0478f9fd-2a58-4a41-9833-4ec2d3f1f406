{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "target": "es2017",
    "sourceMap": true,
    "outDir": "./dist",
    "skipLibCheck": true,
    "incremental": true,
    "esModuleInterop": true,
    "noImplicitAny": false,
    "allowJs": true,
    "baseUrl": "./src",
    "paths": {
      "@clinify/*": [ "./*" ],
      "@mocks/*": [ "./__mocks__/*" ],
      "@fixtures/*": [ "./utils/tests/*" ],
    },
    "lib": ["es2019", "dom"],
    "plugins": [
      {
        "name": "typescript-tslint-plugin"
      }
    ],
    "resolveJsonModule": true
  },
  "include": ["src"],
  "exclude": ["node_modules"]
}
