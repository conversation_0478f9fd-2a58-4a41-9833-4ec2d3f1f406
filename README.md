# Clinify API
## Description

Clinify API repository using [Nest](https://github.com/nestjs/nest) framework

## Installation

```bash
$ yarn install
```

## Run Postgres locally

You can decide to install postgress locally or run it via docker

#### Docker steps

```bash
docker-compose up
```

You can use a `-d` flag for running in background.

Make sure to not have another postgres service running in the background, otherwise you won't be able to connect to the database via a postgres client.

## Migrations

After successfully connecting to the database, you'd need to run migrations to populate the database with tables.

Use command

```bash
yarn migrate
```

## Running the app

```bash
# development
$ yarn start

# watch mode
$ yarn start:dev

# production mode
$ yarn start:prod
```

## Test

```bash
# unit tests
$ yarn test
```

## Permissions

#### WWASF
- We want to be able to assign base permissions to users.
- We want to be able to  dynamically update the permission of a user for manage access.


#### Resources
- [Permission Documentation on Notion](https://www.notion.so/Permissions-20275a4e0cb544738160f3dc3227139e)
- [CASL Documentation](https://casl.js.org/v4/en)

#### Components
- User Abilities
- Authorization guard
- Permission Decorator.
