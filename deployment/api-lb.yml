AWSTemplateFormatVersion: "2010-09-09"
Description: "Create loadbalancer for clinify API"

Parameters:
  vpcId:
    Type: String
    Description: ID of vpc to map network connection to
    Default: vpc-00ea8ae5c9d87d48b
  publicSubnetIds:
    Type: String
    Description: ID's for application subnet
    Default: "subnet-01420e88a981b0271,subnet-0668d6f0554287d00"

Resources:
  AppLoadbalancerSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: LoadbalancerSecurityGroup
      GroupDescription: Security group for loadbalancer
      VpcId: !Ref vpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0

  ServiceLBListenerHTTP:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: redirect
          RedirectConfig:
            Protocol: HTTPS
            Port: 443
            Host: "#{host}"
            Path: "/#{path}"
            Query: "#{query}"
            StatusCode: "HTTP_301"
      LoadBalancerArn: !Ref ServiceLoadBalancer
      Port: 80
      Protocol: HTTP

  ServiceLBListenerHTTPS:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      Certificates:
        - CertificateArn: "arn:aws:acm:eu-west-1:689621821214:certificate/d8f48c7d-663b-429f-8c3d-2d305d64e3f5"
      DefaultActions:
        - Type: fixed-response
          FixedResponseConfig:
            ContentType: text/plain
            MessageBody: "invalid request"
            StatusCode: 504
      LoadBalancerArn: !Ref ServiceLoadBalancer
      Port: 443
      Protocol: HTTPS
    DependsOn:
      - ServiceLoadBalancer

  ServiceLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: ApiLoadbalancer
      Scheme: internet-facing
      Type: application
      Subnets: !Split [",", !Ref publicSubnetIds]
      SecurityGroups:
        - !Ref AppLoadbalancerSecurityGroup
      Tags:
        - Key: Name
          Value: ApiLoadbalancer

Outputs:
  LoadbalancerOut:
    Value: !Ref ServiceLoadBalancer
    Export:
      Name: ServiceLoadBalancerArn
  AppLoadbalancerSecurityGroupOut:
    Value: !Ref AppLoadbalancerSecurityGroup
    Export:
      Name: ServiceLoadBalancerSecurityGroup
  ServiceLBListenerHTTPSOut:
    Value: !Ref ServiceLBListenerHTTPS
    Export:
      Name: ServiceLBListenerHTTPS
