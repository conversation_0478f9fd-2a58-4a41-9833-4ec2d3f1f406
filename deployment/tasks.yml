AWSTemplateFormatVersion: "2010-09-09"
Description: "Create task definition for clinify API"

Resources:
  ECSTaskRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Service:
                - "ecs-tasks.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      Path: "/"
      ManagedPolicyArns:
        - "arn:aws:iam::aws:policy/CloudWatchLogsFullAccess"

  ECSExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: "Allow"
            Principal:
              Service:
                - "ecs-tasks.amazonaws.com"
            Action:
              - "sts:AssumeRole"
      Path: "/"
      ManagedPolicyArns:
        - "arn:aws:iam::aws:policy/CloudWatchLogsFullAccess"
        - "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
        - "arn:aws:iam::aws:policy/AmazonSSMReadOnlyAccess"

  StagingTaskDefinition:
    Type: "AWS::ECS::TaskDefinition"
    Properties:
      Family: api-staging
      ExecutionRoleArn: !GetAtt ECSExecutionRole.Arn
      TaskRoleArn: !GetAtt ECSTaskRole.Arn
      NetworkMode: awsvpc
      ContainerDefinitions:
        - Name: api
          Image: ************.dkr.ecr.eu-west-1.amazonaws.com/clinify-api:staging-0812825eb
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: "/ecs/api-staging"
              awslogs-region: "eu-west-1"
              awslogs-create-group: "true"
              awslogs-stream-prefix: "ecs"
          PortMappings:
            - ContainerPort: 3000
              Protocol: tcp
          Environment:
            - Name: PORT
              Value: 3000
          Secrets:
            - Name: AUTO_MAIL_ACTIVE_LIST_BEFORE_DAYS
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/AUTO_MAIL_ACTIVE_LIST_BEFORE_DAYS
            - Name: CLOUDINARY_API_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/CLOUDINARY_API_KEY
            - Name: CLOUDWATCH_STREAM_NAME
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/CLOUDWATCH_STREAM_NAME
            - Name: DRUG_BANK_TOKEN
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/DRUG_BANK_TOKEN
            - Name: ICD_CLIENT_ID
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/ICD_CLIENT_ID
            - Name: ICD_CLIENT_SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/ICD_CLIENT_SECRET
            - Name: MEET_BASE_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/MEET_BASE_URL
            - Name: MONGO_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/MONGO_URL
            - Name: NODE_ENV
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/NODE_ENV
            - Name: SEND_GRID_USERNAME
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/SEND_GRID_USERNAME
            - Name: GENERATED_TOKEN_SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/GENERATED_TOKEN_SECRET
            - Name: ICD_TOKEN_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/ICD_TOKEN_URL
            - Name: PAYMENT_DEAD_LETTER_QUEUE_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/PAYMENT_DEAD_LETTER_QUEUE_URL
            - Name: PAYSTACK_BASEURL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/PAYSTACK_BASEURL
            - Name: PGPASSWORD
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/PGPASSWORD
            - Name: SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/SECRET
            - Name: SEND_GRID_PWD
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/SEND_GRID_PWD
            - Name: TRANSACTION_REFERENCE_SALT
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/TRANSACTION_REFERENCE_SALT
            - Name: WEMA_BASE_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/WEMA_BASE_URL
            - Name: WEMA_FAITH_SUBSCRIPTION_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/WEMA_FAITH_SUBSCRIPTION_KEY
            - Name: AUTO_APPROVE_WITHDRAWALS
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/AUTO_APPROVE_WITHDRAWALS
            - Name: AWS_ACCESS_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/AWS_ACCESS_KEY
            - Name: AWS_ACCESS_KEY_ID
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/AWS_ACCESS_KEY_ID
            - Name: CLOUDINARY_API_SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/CLOUDINARY_API_SECRET
            - Name: MEMCACHEDCLOUD_SERVERS
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/MEMCACHEDCLOUD_SERVERS
            - Name: PGPORT
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/PGPORT
            - Name: RINGCAPTCHA_API_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/RINGCAPTCHA_API_KEY
            - Name: RINGCAPTCHA_APP_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/RINGCAPTCHA_APP_KEY
            - Name: WEMA_TRANSACTION_QUERY_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/WEMA_TRANSACTION_QUERY_URL
            - Name: WEMA_VA_PREFIX
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/WEMA_VA_PREFIX
            - Name: AWS_KEY_SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/AWS_KEY_SECRET
            - Name: AWS_SECRET_ACCESS_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/AWS_SECRET_ACCESS_KEY
            - Name: CANCELLATION_LEVY_CLINIFY_COMISSION
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/CANCELLATION_LEVY_CLINIFY_COMISSION
            - Name: CLOUDWATCH_GROUP_NAME
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/CLOUDWATCH_GROUP_NAME
            - Name: MEET_TOKEN_SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/MEET_TOKEN_SECRET
            - Name: PGHOST
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/PGHOST
            - Name: PGUSER
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/PGUSER
            - Name: REDIS_HOST
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/REDIS_HOST
            - Name: CANCELLATION_LEVY_PERCENT
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/CANCELLATION_LEVY_PERCENT
            - Name: CLOUDINARY_CLOUD_NAME
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/CLOUDINARY_CLOUD_NAME
            - Name: CLOUDINARY_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/CLOUDINARY_URL
            - Name: INTEGRATION_TOKEN
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/INTEGRATION_TOKEN
            - Name: LEADWAY_BASE_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/LEADWAY_BASE_URL
            - Name: PAYMENT_QUEUE_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/PAYMENT_QUEUE_URL
            - Name: PAYSTACK_SECRET_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/PAYSTACK_SECRET_KEY
            - Name: PGDATABASE
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/PGDATABASE
            - Name: WEMA_GET_CUSTOMER_DETAILS_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/WEMA_GET_CUSTOMER_DETAILS_URL
            - Name: WEMA_VENDOR_ID
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/WEMA_VENDOR_ID
            - Name: FEEDBACK_CREDENTIALS_EMAIL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/FEEDBACK_CREDENTIALS_EMAIL
            - Name: FEEDBACK_CREDENTIALS_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/FEEDBACK_CREDENTIALS_KEY
            - Name: FEEDBACK_SHEET_ID
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/FEEDBACK_SHEET_ID
            - Name: MINIMUM_WITHDRAWABLE_AMOUNT
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/MINIMUM_WITHDRAWABLE_AMOUNT
            - Name: PERCENTAGE_TRANSACTION_CHARGE
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/PERCENTAGE_TRANSACTION_CHARGE
            - Name: REDIS_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/REDIS_URL
            - Name: WEB_CONCURRENCY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/WEB_CONCURRENCY
            - Name: AWS_S3_INVENTORY_BUCKET_NAME
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/AWS_S3_INVENTORY_BUCKET_NAME
            - Name: INVENTORY_S3_ENDPOINT
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/staging/INVENTORY_S3_ENDPOINT
      RequiresCompatibilities:
        - FARGATE
      Cpu: "512"
      Memory: "1024"

  ProdTaskDefinition:
    Type: "AWS::ECS::TaskDefinition"
    Properties:
      Family: api-prod
      ExecutionRoleArn: !GetAtt ECSExecutionRole.Arn
      TaskRoleArn: !GetAtt ECSTaskRole.Arn
      NetworkMode: awsvpc
      ContainerDefinitions:
        - Name: api
          Image: ************.dkr.ecr.eu-west-1.amazonaws.com/clinify-api:master-dc8e016c1
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: "/ecs/api-staging"
              awslogs-region: "eu-west-1"
              awslogs-create-group: "true"
              awslogs-stream-prefix: "ecs"
          PortMappings:
            - ContainerPort: 3000
              Protocol: tcp
          Environment:
            - Name: PORT
              Value: 3000
          Secrets:
            - Name: AUTO_MAIL_ACTIVE_LIST_BEFORE_DAYS
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/AUTO_MAIL_ACTIVE_LIST_BEFORE_DAYS
            - Name: CLOUDINARY_API_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/CLOUDINARY_API_KEY
            - Name: CLOUDWATCH_STREAM_NAME
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/CLOUDWATCH_STREAM_NAME
            - Name: DRUG_BANK_TOKEN
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/DRUG_BANK_TOKEN
            - Name: ICD_CLIENT_ID
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/ICD_CLIENT_ID
            - Name: ICD_CLIENT_SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/ICD_CLIENT_SECRET
            - Name: MEET_BASE_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/MEET_BASE_URL
            - Name: MONGO_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/MONGO_URL
            - Name: NODE_ENV
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/NODE_ENV
            - Name: SEND_GRID_USERNAME
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/SEND_GRID_USERNAME
            - Name: GENERATED_TOKEN_SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/GENERATED_TOKEN_SECRET
            - Name: ICD_TOKEN_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/ICD_TOKEN_URL
            - Name: PAYMENT_DEAD_LETTER_QUEUE_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/PAYMENT_DEAD_LETTER_QUEUE_URL
            - Name: PAYSTACK_BASEURL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/PAYSTACK_BASEURL
            - Name: PGPASSWORD
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/PGPASSWORD
            - Name: SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/SECRET
            - Name: SEND_GRID_PWD
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/SEND_GRID_PWD
            - Name: TRANSACTION_REFERENCE_SALT
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/TRANSACTION_REFERENCE_SALT
            - Name: WEMA_BASE_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/WEMA_BASE_URL
            - Name: WEMA_FAITH_SUBSCRIPTION_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/WEMA_FAITH_SUBSCRIPTION_KEY
            - Name: AUTO_APPROVE_WITHDRAWALS
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/AUTO_APPROVE_WITHDRAWALS
            - Name: AWS_ACCESS_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/AWS_ACCESS_KEY
            - Name: AWS_ACCESS_KEY_ID
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/AWS_ACCESS_KEY_ID
            - Name: CLOUDINARY_API_SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/CLOUDINARY_API_SECRET
            - Name: PGPORT
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/PGPORT
            - Name: RINGCAPTCHA_API_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/RINGCAPTCHA_API_KEY
            - Name: RINGCAPTCHA_APP_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/RINGCAPTCHA_APP_KEY
            - Name: WEMA_TRANSACTION_QUERY_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/WEMA_TRANSACTION_QUERY_URL
            - Name: WEMA_VA_PREFIX
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/WEMA_VA_PREFIX
            - Name: AWS_KEY_SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/AWS_KEY_SECRET
            - Name: AWS_SECRET_ACCESS_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/AWS_SECRET_ACCESS_KEY
            - Name: CANCELLATION_LEVY_CLINIFY_COMISSION
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/CANCELLATION_LEVY_CLINIFY_COMISSION
            - Name: CLOUDWATCH_GROUP_NAME
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/CLOUDWATCH_GROUP_NAME
            - Name: MEET_TOKEN_SECRET
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/MEET_TOKEN_SECRET
            - Name: PGHOST
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/PGHOST
            - Name: PGUSER
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/PGUSER
            - Name: REDIS_HOST
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/REDIS_HOST
            - Name: CANCELLATION_LEVY_PERCENT
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/CANCELLATION_LEVY_PERCENT
            - Name: CLOUDINARY_CLOUD_NAME
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/CLOUDINARY_CLOUD_NAME
            - Name: CLOUDINARY_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/CLOUDINARY_URL
            - Name: INTEGRATION_TOKEN
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/INTEGRATION_TOKEN
            - Name: LEADWAY_BASE_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/LEADWAY_BASE_URL
            - Name: PAYMENT_QUEUE_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/PAYMENT_QUEUE_URL
            - Name: PAYSTACK_SECRET_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/PAYSTACK_SECRET_KEY
            - Name: PGDATABASE
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/PGDATABASE
            - Name: WEMA_GET_CUSTOMER_DETAILS_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/WEMA_GET_CUSTOMER_DETAILS_URL
            - Name: WEMA_VENDOR_ID
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/WEMA_VENDOR_ID
            - Name: FEEDBACK_CREDENTIALS_EMAIL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/FEEDBACK_CREDENTIALS_EMAIL
            - Name: FEEDBACK_CREDENTIALS_KEY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/FEEDBACK_CREDENTIALS_KEY
            - Name: FEEDBACK_SHEET_ID
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/FEEDBACK_SHEET_ID
            - Name: MINIMUM_WITHDRAWABLE_AMOUNT
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/MINIMUM_WITHDRAWABLE_AMOUNT
            - Name: PERCENTAGE_TRANSACTION_CHARGE
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/PERCENTAGE_TRANSACTION_CHARGE
            - Name: REDIS_URL
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/REDIS_URL
            - Name: WEB_CONCURRENCY
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/WEB_CONCURRENCY
            - Name: AWS_S3_INVENTORY_BUCKET_NAME
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/AWS_S3_INVENTORY_BUCKET_NAME
            - Name: INVENTORY_S3_ENDPOINT
              ValueFrom: arn:aws:ssm:eu-west-1:************:parameter/api/master/INVENTORY_S3_ENDPOINT
      RequiresCompatibilities:
        - FARGATE
      Cpu: "512"
      Memory: "1024"

Outputs:
  TaskStagingOut:
    Value: !Ref StagingTaskDefinition
    Export:
      Name: StagingTaskDefinition
  TaskProdOut:
    Value: !Ref ProdTaskDefinition
    Export:
      Name: ProdTaskDefinition
