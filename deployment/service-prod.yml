AWSTemplateFormatVersion: "2010-09-09"
Description: "Deploy clinify prod api"

Parameters:
  vpcId:
    Type: String
    Description: ID of vpc to map network connection to
    Default: vpc-00ea8ae5c9d87d48b
  privateSubnetIds:
    Type: String
    Description: ID's for application subnet
    Default: "subnet-0e05f6c5635b5295e,subnet-0dce6ee4d2a20472e"
  publicSubnetIds:
    Type: String
    Description: ID's for application subnet
    Default: "subnet-01420e88a981b0271,subnet-0668d6f0554287d00"

Resources:
  ProdAPISecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: ProdApiSecurityGroup
      GroupDescription: Security group for backend api
      VpcId: !Ref vpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          SourceSecurityGroupId: !ImportValue ServiceLoadBalancerSecurityGroup
        - IpProtocol: tcp
          FromPort: 3000
          ToPort: 3000
          SourceSecurityGroupId: !ImportValue ServiceLoadBalancerSecurityGroup

  ProdECSTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: prod-ecs-target-group
      Port: 80
      Protocol: HTTP
      VpcId: !Ref vpcId
      TargetType: ip
      HealthCheckIntervalSeconds: 30
      HealthCheckProtocol: HTTP
      HealthCheckPath: /health
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 5
      UnhealthyThresholdCount: 5

  ProdListenerRule:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      ListenerArn: !ImportValue ServiceLBListenerHTTPS
      Priority: 1
      Conditions:
        - Field: host-header
          Values:
            - api.myclinify.com
      Actions:
        - Type: forward
          TargetGroupArn: !Ref ProdECSTargetGroup
    DependsOn:
      - ProdECSTargetGroup

  ProdECSService:
    Type: "AWS::ECS::Service"
    Properties:
      ServiceName: api-prod
      Cluster: !ImportValue BackendAPICluster
      LaunchType: FARGATE
      DesiredCount: 1
      TaskDefinition: !ImportValue ProdTaskDefinition
      DeploymentConfiguration:
        DeploymentCircuitBreaker:
          Enabled: true
          RollBack: true
      HealthCheckGracePeriodSeconds: 300
      LoadBalancers:
        - ContainerName: api
          ContainerPort: 3000
          TargetGroupArn: !Ref ProdECSTargetGroup
      NetworkConfiguration:
        AwsvpcConfiguration:
          # AssignPublicIp: ENABLED
          Subnets: !Split [",", !Ref privateSubnetIds]
          SecurityGroups:
            - !Ref ProdAPISecurityGroup
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 50
    DependsOn:
      - ProdListenerRule


  #-----------------------------------------------------------
  #  AUTO SCALING
  # -----------------------------------------------------------

  AutoScalingRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ecs-tasks.amazonaws.com
            Action:
              - 'sts:AssumeRole'
      Path: '/'
      Policies:
        - PolicyName: root
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ecs:DescribeServices
                  - ecs:UpdateService
                  - cloudwatch:DeleteAlarms
                  - cloudwatch:DescribeAlarms
                  - cloudwatch:PutMetricAlarm
                Resource: '*'

  AutoScalingTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Properties:
      MinCapacity: 1
      MaxCapacity: 2
      ResourceId: !Join
        - '/'
        - - service
          - !ImportValue BackendAPICluster
          - !GetAtt ProdECSService.Name
      ScalableDimension: ecs:service:DesiredCount
      ServiceNamespace: ecs
      RoleARN: !GetAtt AutoScalingRole.Arn

  ScaleUpPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub '${ProdECSService}ScaleUpPolicy'
      PolicyType: StepScaling
      ScalingTargetId: !Ref AutoScalingTarget
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown: 60
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalLowerBound: 0
            ScalingAdjustment: 1

  ScaleDownPolicy:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Properties:
      PolicyName: !Sub '${ProdECSService}ScaleDownPolicy'
      PolicyType: StepScaling
      ScalingTargetId: !Ref AutoScalingTarget
      StepScalingPolicyConfiguration:
        AdjustmentType: ChangeInCapacity
        Cooldown: 60
        MetricAggregationType: Average
        StepAdjustments:
          - MetricIntervalUpperBound: 0
            ScalingAdjustment: -1

  AlarmHighRequests:
    Type: AWS::CloudWatch::Alarm
    Properties:
      ActionsEnabled: TRUE
      AlarmActions:
        - !Ref ScaleUpPolicy
      AlarmDescription: !Sub
        - 'Scale Up Alarm based on requests for ${ProdECSService}'
        - FargateServiceName: !GetAtt ProdECSService.Name
      ComparisonOperator: GreaterThanThreshold
      DatapointsToAlarm: 2
      Dimensions:
        - Name: TargetGroup
          Value: !GetAtt ProdECSTargetGroup.TargetGroupFullName
      EvaluationPeriods: 3
      MetricName: RequestCountPerTarget
      Namespace: AWS/ApplicationELB
      OKActions:
        - !Ref ScaleDownPolicy
      Period: 60
      Statistic: Sum
      Threshold: 3000 ## update this after testing
      TreatMissingData: ignore
      Unit: None
