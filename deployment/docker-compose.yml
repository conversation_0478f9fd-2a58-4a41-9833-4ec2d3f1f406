version: '3'
services:
  clinify:
    image: 689621821214.dkr.ecr.eu-west-1.amazonaws.com/clinify-api:{{IMAGE}}
    container_name: clinify
    ports:
      - 80:3200
    env_file:
      - .env
    depends_on:
      - migration
  migration:
    image: 689621821214.dkr.ecr.eu-west-1.amazonaws.com/clinify-api:{{IMAGE}}
    container_name: migrations
    command: ["yarn", "migration:run"]
    env_file:
      - .env
