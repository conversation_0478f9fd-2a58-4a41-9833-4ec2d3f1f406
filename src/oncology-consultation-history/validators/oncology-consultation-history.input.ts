import { Optional } from '@nestjs/common';
import {
  Field,
  InputType,
  ObjectType,
  OmitType,
  PartialType,
  registerEnumType,
} from '@nestjs/graphql';
import { IsOptional, IsUUID } from 'class-validator';
import { SelectionInput } from '@clinify/consultations/validators/consultation.input';
import { NewHmoClaimInput } from '@clinify/hmo-claims/inputs/hmo-claims.input';
import { ServiceDetailInput } from '@clinify/shared/validators/service-detail.input';
import { TumorDetailsInput } from '@clinify/users/inputs/oncology-history.input';

export enum OncologyConsultationLinkedRecordType {
  VitalSign = 'Vital Signs',
  Admission = 'Admission',
  Allergy = 'Allergy',
  LabTest = 'Lab Test',
  Investigation = 'Investigation',
  Medication = 'Medication',
  Radiology = 'Radiology Exam',
  Procedure = 'Procedure',
  NursingService = 'Nursing Service',
}

registerEnumType(OncologyConsultationLinkedRecordType, {
  name: 'OncologyConsultationLinkedRecordType',
  description: 'Record types that can be linked to Oncology Consultation',
});

@InputType('OncologyDrugAdministrationRegistrationInput')
@ObjectType()
export class OncologyDrugAdministrationRegistration {
  @Field()
  period: string;

  @Field({ nullable: true })
  administeredBy?: string;

  @Field({ nullable: true })
  administratorId?: string;

  @Field({ nullable: true })
  administrationDateTime?: Date;
}

@InputType('ChemoInvestigationDetailsInput')
@ObjectType()
export class ChemoInvestigationDetails {
  @Field({ nullable: true })
  investigationType?: string;

  @Field({ nullable: true })
  investigationName?: string;

  @Field({ nullable: true })
  investigationPerformed?: boolean;

  @Field({ nullable: true })
  investigationVerified?: boolean;
}

@InputType()
export class OncologyChemoDrugInput {
  @Field(() => String, { nullable: false })
  day: string;

  @Field(() => String, { nullable: false })
  drugName: string;

  @Field(() => String, { nullable: true })
  drugId: string;

  @Field(() => String, { nullable: true })
  dosage: string;

  @Field(() => String, { nullable: true })
  dosagePercentage: string;

  @Field(() => String, { nullable: true })
  totalDose?: string;

  @Field(() => String, { nullable: true })
  adjustedDose?: string;

  @Field(() => String, { nullable: true })
  quantity?: string;

  @Field(() => String, { nullable: true })
  route: string;

  @Field(() => String, { nullable: true })
  infusionUsed: string;

  @Field(() => String, { nullable: true })
  note: string;

  @Field({ nullable: true })
  @Optional()
  type: string;

  @Field({ nullable: true })
  combinationName: string;

  @Field({ nullable: true })
  chemoDiagnosis?: string;

  @Field(() => [ChemoInvestigationDetails], { nullable: true })
  investigationDetails?: ChemoInvestigationDetails[];

  @Field({ nullable: true })
  combinationGroupName: string;

  @Field({ nullable: false })
  cycleNumber: number;

  @Field({ nullable: true })
  @Optional()
  id?: string;

  @Field()
  section: string;

  @Field({ nullable: true })
  frequency?: string;

  @Field(() => String, { nullable: true })
  inventoryClass?: string;

  @Field(() => [OncologyDrugAdministrationRegistration], { nullable: true })
  administrationRegister?: OncologyDrugAdministrationRegistration[];
}

@InputType()
export class NewOncologyChemoDrugSingleInput extends PartialType(
  OmitType(OncologyChemoDrugInput, ['id']),
) {
  @Field({ nullable: false })
  oncologyConsultationHistoryId: string;
}

@InputType()
export class OncologyChemoDrugSingleInput extends PartialType(
  OmitType(NewOncologyChemoDrugSingleInput, ['oncologyConsultationHistoryId']),
) {
  @Field({ nullable: false })
  id: string;
}

@InputType()
export class NewOncologyConsultationInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsUUID('4')
  id?: string;

  @Field(() => String, { nullable: false })
  clinifyId: string;

  @Field({ nullable: true })
  consultationDateTime: Date;

  @Field({ nullable: true })
  duration: string;

  @Field({ nullable: true })
  priority: string;

  @Field({ nullable: true })
  category: string;

  @Field({ nullable: true })
  doctorName: string;

  @Field({ nullable: true })
  specialty: string;

  @Field({ nullable: true })
  rank: string;

  @Field({ nullable: true })
  department: string;

  @Field({ nullable: true })
  consultationStartDate: Date;

  @Field({ nullable: true })
  consultationEndDate: Date;

  @Field({ nullable: true })
  facilityName: string;

  @Field({ nullable: true })
  facilityAddress: string;

  @Field(() => String, { nullable: true })
  initialDiagnosisICD10?: string;

  @Field(() => String, { nullable: true })
  initialDiagnosisICD11?: string;

  @Field(() => String, { nullable: true })
  initialDiagnosisSNOMED?: string;

  @Field(() => String, { nullable: true })
  finalDiagnosisICD10: string;

  @Field(() => String, { nullable: true })
  finalDiagnosisICD11: string;

  @Field(() => String, { nullable: true })
  finalDiagnosisSNOMED: string;

  @Field(() => String, { nullable: true })
  diagnosedBy: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  diagnosisDateTime?: Date;

  @Field(() => String, { nullable: true })
  additionalNote?: string;

  @Field(() => String, { nullable: true })
  stageDiagnosisICD10?: string;

  @Field(() => String, { nullable: true })
  stageDiagnosisICD11?: string;

  @Field(() => String, { nullable: true })
  stageDiagnosisSNOMED?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  stageDiagnosisDateTime?: Date;

  @Field(() => String, { nullable: true })
  classification?: string;

  @Field(() => String, { nullable: true })
  stage?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  stageDate?: Date;

  @Field(() => String, { nullable: true })
  histopathologicType?: string;

  @Field(() => String, { nullable: true })
  stageTiming?: string;

  @Field(() => String, { nullable: true })
  primaryTumor?: string;

  @Field(() => String, { nullable: true })
  residualTumor?: string;

  @Field(() => [TumorDetailsInput], { nullable: true })
  tumorDetails?: TumorDetailsInput[];

  @Field(() => String, { nullable: true })
  lymphovascularInvasion?: string;

  @Field(() => String, { nullable: true })
  regionalLymphNodes?: string;

  @Field(() => String, { nullable: true })
  numberOfNodes?: string;

  @Field(() => String, { nullable: true })
  distantMetastasis?: string;

  @Field(() => String, { nullable: true })
  grade?: string;

  @Field(() => String, { nullable: true })
  stageStatus?: string;

  @Field(() => String, { nullable: true })
  cancerType?: string;

  @Field(() => String, { nullable: true })
  progression?: string;

  @Field(() => String, { nullable: true })
  relapse?: string;

  @Field(() => String, { nullable: true })
  remission?: string;

  @Field(() => String, { nullable: true })
  stageTreatmentType?: string;

  @Field(() => String, { nullable: true })
  stageAdditionalNote?: string;

  @Field(() => String, { nullable: true })
  nuclearGradeOrPleomorphism?: string;

  @Field(() => String, { nullable: true })
  mitoticCountScore?: string;

  @Field(() => String, { nullable: true })
  tubuleFormation?: string;

  @Field(() => String, { nullable: true })
  stageGroupingScore?: string;

  @Field(() => String, { nullable: true })
  scarffBloomRichardsonScore?: string;

  @Field(() => String, { nullable: true })
  nottinghamModificationSbrGrade?: string;

  @Field(() => String, { nullable: true })
  stageLymphovascularInvasion?: string;

  @Field(() => String, { nullable: true })
  stageHistopathologicType?: string;

  @Field(() => String, { nullable: true })
  diagnosticInformation?: string;

  @Field(() => String, { nullable: true })
  typeOfSpecimen?: string;

  @Field(() => String, { nullable: true })
  stagingRole?: string;

  @Field(() => Date, { nullable: true })
  stagingDate?: Date;

  @Field(() => String, { nullable: true })
  treatmentType?: string;

  @Field(() => String, { nullable: true })
  treatmentSite?: string;

  @Field(() => String, { nullable: true })
  intentOfTreatment?: string;

  @Field(() => String, { nullable: true })
  lineOfTreatment?: string;

  @Field(() => String, { nullable: true })
  concurrentTreatment?: string;

  @Field(() => String, { nullable: true })
  treatmentPlanProvider?: string;

  @Field(() => String, { nullable: true })
  treatmentDepartment?: string;

  @Field(() => String, { nullable: true })
  treatmentStatus?: string;

  @Field(() => String, { nullable: true })
  treatmentPriority?: string;

  @Field(() => String, { nullable: true })
  treatmentInterval?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  treatmentStartDate?: Date;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  treatmentEndDate?: Date;

  @Field(() => String, { nullable: true })
  treatmentCycleDays?: string;

  @Field(() => String, { nullable: true })
  treatmentCycleNumber?: string;

  @Field(() => String, { nullable: true })
  treatmentPatientType?: string;

  @Field(() => String, { nullable: true })
  treatmentAdverseReaction?: string;

  @Field(() => String, { nullable: true })
  treatmentSpecificReaction?: string;

  @Field(() => String, { nullable: true })
  treatmentOutcome?: string;

  @Field(() => String, { nullable: true })
  treatmentResponse?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  treatmentFollowupDate?: Date;

  @Field(() => String, { nullable: true })
  treatmentAdditionalNote?: string;

  @Field(() => String, { nullable: true })
  therapyType?: string;

  @Field(() => String, { nullable: true })
  therapySite?: string;

  @Field(() => String, { nullable: true })
  intentOfTherapy?: string;

  @Field(() => String, { nullable: true })
  lineOfTherapy?: string;

  @Field(() => String, { nullable: true })
  concurrentTherapy?: string;

  @Field(() => String, { nullable: true })
  therapyPlanProvider?: string;

  @Field(() => String, { nullable: true })
  therapyDepartment?: string;

  @Field(() => String, { nullable: true })
  therapyStatus?: string;

  @Field(() => String, { nullable: true })
  therapyPriority?: string;

  @Field(() => String, { nullable: true })
  therapyInterval?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  therapyStartDate?: Date;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  therapyEndDate?: Date;

  @Field(() => String, { nullable: true })
  therapyCycleDays?: string;

  @Field(() => String, { nullable: true })
  therapyCycleNumber?: string;

  @Field(() => String, { nullable: true })
  therapyPatientType?: string;

  @Field(() => String, { nullable: true })
  therapyAdverseReaction?: string;

  @Field(() => String, { nullable: true })
  therapySpecificReaction?: string;

  @Field(() => String, { nullable: true })
  therapyOutcome?: string;

  @Field(() => String, { nullable: true })
  therapyResponse?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  therapyFollowupDate?: Date;

  @Field(() => String, { nullable: true })
  therapyAdditionalNote?: string;

  @Field(() => String, { nullable: true })
  nottinghamGradeAbove?: string;

  @Field(() => String, { nullable: true })
  estrogenReceptorExpression?: string;

  @Field(() => String, { nullable: true })
  erPercentagePositive?: string;

  @Field(() => String, { nullable: true })
  erAllredScore?: string;

  @Field(() => String, { nullable: true })
  progesteroneReceptor?: string;

  @Field(() => String, { nullable: true })
  prPercentagePositive?: string;

  @Field(() => String, { nullable: true })
  prAllredScore?: string;

  @Field(() => String, { nullable: true })
  overallHer2Status?: string;

  @Field(() => String, { nullable: true })
  ihcScore: string;

  @Field(() => String, { nullable: true })
  fishResult?: string;

  @Field(() => String, { nullable: true })
  fishCopyNumber?: string;

  @Field(() => String, { nullable: true })
  her2OrCep17Ratio?: string;

  @Field(() => String, { nullable: true })
  circulatingTumorCells?: string;

  @Field(() => String, { nullable: true })
  complaintGender?: string;

  @Field(() => String, { nullable: true })
  complaintSmartText?: string;

  @Field(() => SelectionInput, { nullable: true })
  complaintSmartSelection: SelectionInput;

  @Field(() => String, { nullable: true })
  systemReviewSmartText?: string;

  @Field(() => SelectionInput, { nullable: true })
  systemReviewSmartSelection: SelectionInput;

  @Field(() => String, { nullable: true })
  physicalExamSmartText?: string;

  @Field(() => SelectionInput, { nullable: true })
  physicalExamSmartSelection: SelectionInput;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealComplaint?: boolean;

  @Field(() => String, { nullable: true })
  complaint: string;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealComplaintHistory?: boolean;

  @Field(() => String, { nullable: true })
  complaintHistory: string;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealSystemReview?: boolean;

  @Field(() => String, { nullable: true })
  systemReview: string;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealPhysicalExam?: boolean;

  @Field(() => String, { nullable: true })
  physicalExam: string;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealAudiometry?: boolean;

  @Field(() => String, { nullable: true })
  audiometry: string;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealHealthEducation?: boolean;

  @Field(() => String, { nullable: true })
  healthEducation: string;

  @Field(() => String, { nullable: true })
  treatmentPlan: string;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealTreatmentPlan?: boolean;

  @Field({ nullable: true })
  treatmentGiven: string;

  @Field({ nullable: true })
  adverseEffectsFollowingTreatment: string;

  @Field({ nullable: true })
  stateEffects: string;

  @Field({ nullable: true })
  adverseEffectsInvestigated: string;

  @Field({ nullable: true })
  outcomeOfInvestigation: string;

  @Field({ nullable: true })
  patientAdmitted?: string;

  @Field({ nullable: true })
  admissionConsent?: string;

  @Field(() => String, { nullable: true })
  patientConsentSignature: string;

  @Field(() => String, { nullable: true })
  patientConsentSignatureType: string;

  @Field(() => String, { nullable: true })
  patientConsentSignatureDateTime: string;

  @Field(() => String, { nullable: true })
  observationNote: string;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealObservationNote?: boolean;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  nextAppointmentDateTime: Date;

  @Field({ nullable: true })
  nextAppointmentEndDateTime: Date;

  @Field(() => String, { nullable: true })
  nextAppointmentDuration?: string;

  @Field(() => String, { nullable: true })
  nextAppointmentSpecialty: string;

  @Field(() => String, { nullable: true })
  nextAppointmentRole: string;

  @Field(() => String, { nullable: true })
  nextAppointmentCategory: string;

  @Field(() => String, { nullable: true })
  nextAppointmentSpecialistId?: string;

  @Field(() => String, { nullable: true })
  nextAppointmentSpecialistName?: string;

  @Field(() => String, { nullable: true })
  nextAppointmentUrgency?: string;

  @Field(() => String, { nullable: true })
  nextAppointmentDeliveryMethod: string;

  @Field(() => [String], { nullable: true })
  documentUrl?: string[];

  @Field(() => String, { nullable: true })
  provider?: string;

  @Field(() => String, { nullable: true })
  providerServiceName?: string;

  @Field(() => [ServiceDetailInput], { nullable: true })
  serviceDetails?: ServiceDetailInput[];

  @Field(() => NewHmoClaimInput, { nullable: true })
  @IsOptional()
  hmoClaim?: NewHmoClaimInput;

  @Field(() => String, { nullable: true })
  @IsUUID('4')
  @IsOptional()
  hmoProviderId?: string;

  @Field(() => [OncologyChemoDrugInput], { nullable: true })
  @IsOptional()
  oncologyChemoDrugs?: OncologyChemoDrugInput[];

  @Field(() => [String], { nullable: true })
  saveAsTemplate?: string[];

  @Field({ nullable: true })
  preChemoTemplateName?: string;

  @Field({ nullable: true })
  chemoTemplateName?: string;

  @Field({ nullable: true })
  postChemoTemplateName?: string;

  @Field(() => Boolean, { defaultValue: true })
  @IsOptional()
  concealChemoNote?: boolean;

  @Field({ nullable: true })
  chemoNote?: string;

  @Field({ nullable: true })
  laboratoryTestVerified?: string;

  @Field({ nullable: true })
  radiologyExaminationVerified?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  admissions: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  allergies: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  labTests: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  investigations: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  medications: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  radiology: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  surgeries: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  vitals: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  nursingServices: string[];
}

@InputType()
export class OncologyConsultationInput extends PartialType(
  OmitType(NewOncologyConsultationInput, ['hmoClaim']),
) {}

@InputType()
export class OncologyChemoCommentInput {
  @Field(() => Number, { nullable: false })
  cycleNumber: number;

  @Field(() => String, { nullable: false })
  section: string;

  @Field(() => String, { nullable: true })
  comment: string;
}
