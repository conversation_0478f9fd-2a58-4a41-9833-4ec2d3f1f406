import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OncologyChemoDrugSubscriber } from './oncology-chemo-drug.subscriber';
import { OncologyChemoDrugModel } from '../models/oncology-chemo-drug.model';
import { TestDataSourceOptions } from '@clinify/data-source';

describe('OncologyChemoDrugSubscriber', () => {
  let subscriber: OncologyChemoDrugSubscriber;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [OncologyChemoDrugSubscriber],
    }).compile();

    subscriber = module.get<OncologyChemoDrugSubscriber>(
      OncologyChemoDrugSubscriber,
    );
  });

  it('should return Oncology Chemo Drug Model class', () => {
    expect(subscriber.listenTo()).toBe(OncologyChemoDrugModel);
  });

  it('beforeInsert(): should get admin schedule before adding new row to oncology_chemo_drug table', () => {
    const event: any = {
      entity: { frequency: '2 Times a Day' },
    };

    subscriber.beforeInsert(event);

    expect(event.entity.administrationRegister).toStrictEqual([
      { period: '1' },
      { period: '2' },
    ]);
  });

  it('beforeUpdate(): should get admin schedule before updation existing row to oncology_chemo_drug table', () => {
    let event: any = {
      entity: { frequency: '2 Times a Day' },
    };
    const result = subscriber.beforeUpdate(event);
    expect(result).toBeFalsy();

    event = {
      entity: { frequency: '2 Times a Day' },
      databaseEntity: { frequency: '2 Times a Day' },
    };
    const result1 = subscriber.beforeUpdate(event);
    expect(result1).toBeFalsy();

    event = {
      entity: { frequency: '3 Times a Day' },
      databaseEntity: {
        frequency: '2 Times a Day',
        administrationRegister: [{ administratorId: 'administrator-id' }],
      },
    };
    expect(() => subscriber.beforeUpdate(event)).toThrowError(
      'Drug Already Administered',
    );

    event = {
      entity: { frequency: '3 Times a Day' },
      databaseEntity: {
        frequency: '2 Times a Day',
        administrationRegister: [{ administratorId: null }],
      },
    };
    subscriber.beforeUpdate(event);
    expect(event.entity.administrationRegister).toStrictEqual([
      { period: '1' },
      { period: '2' },
      { period: '3' },
    ]);
  });
});
