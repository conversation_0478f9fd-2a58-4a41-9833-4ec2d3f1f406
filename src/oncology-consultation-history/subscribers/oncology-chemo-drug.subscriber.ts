import { ConflictException, Injectable } from '@nestjs/common';
import {
  DataSource,
  EntitySubscriberInterface,
  InsertEvent,
  UpdateEvent,
} from 'typeorm';
import { OncologyChemoDrugModel } from '@clinify/oncology-consultation-history/models/oncology-chemo-drug.model';
import { OncologyDrugAdministrationRegistration } from '@clinify/oncology-consultation-history/validators/oncology-consultation-history.input';

@Injectable()
export class OncologyChemoDrugSubscriber implements EntitySubscriberInterface {
  constructor(private dataSource: DataSource) {
    this.dataSource.manager.connection.subscribers.push(this);
  }
  listenTo(): any {
    return OncologyChemoDrugModel;
  }

  private generateAdminSchedule(
    frequency: string,
  ): OncologyDrugAdministrationRegistration[] {
    switch (frequency) {
      case '2 Times a Day': {
        return [{ period: '1' }, { period: '2' }];
      }
      case '3 Times a Day': {
        return [{ period: '1' }, { period: '2' }, { period: '3' }];
      }
      case '4 Times a Day': {
        return [
          { period: '1' },
          { period: '2' },
          { period: '3' },
          { period: '4' },
        ];
      }
      case 'Hourly': {
        return [
          { period: '1H' },
          { period: '2H' },
          { period: '3H' },
          { period: '4H' },
          { period: '5H' },
          { period: '6H' },
          { period: '7H' },
          { period: '8H' },
          { period: '9H' },
          { period: '10H' },
          { period: '11H' },
          { period: '12H' },
          { period: '13H' },
          { period: '14H' },
          { period: '15H' },
          { period: '16H' },
          { period: '17H' },
          { period: '18H' },
          { period: '19H' },
          { period: '20H' },
          { period: '21H' },
          { period: '22H' },
          { period: '23H' },
          { period: '24H' },
        ];
      }
      case 'Every 6 Hours': {
        return [
          { period: '6H' },
          { period: '12H' },
          { period: '18H' },
          { period: '24H' },
        ];
      }
      case 'Every 8 Hours': {
        return [{ period: '8H' }, { period: '16H' }, { period: '24H' }];
      }
      case 'Every 10 Hours': {
        return [{ period: '10H' }, { period: '20H' }];
      }
      case 'Every 12 Hours': {
        return [{ period: '12H' }, { period: '24H' }];
      }
      case 'STAT':
      case 'At Bedtime':
      case 'Nocte':
      case 'Qd':
      case 'At Morning':
      case 'Mane':
      case 'When Necessary':
      default:
        return [{ period: '' }];
    }
  }

  beforeInsert(event: InsertEvent<OncologyChemoDrugModel>) {
    event.entity.administrationRegister = this.generateAdminSchedule(
      event.entity.frequency,
    );
  }

  beforeUpdate(event: UpdateEvent<OncologyChemoDrugModel>) {
    const existing = event.databaseEntity;
    if (!existing) return;
    if (existing.frequency === event.entity.frequency) return;
    const hasBeenAdministered = existing.administrationRegister.some(
      (item) => !!item.administratorId,
    );
    if (hasBeenAdministered && event.entity.frequency !== existing.frequency) {
      throw new ConflictException('Drug Already Administered');
    }
    event.entity.administrationRegister = this.generateAdminSchedule(
      event.entity.frequency,
    );
  }
}
