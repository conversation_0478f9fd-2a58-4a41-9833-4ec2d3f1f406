/* eslint-disable max-lines */
import { NotAcceptableException, NotFoundException } from '@nestjs/common';
import cloneDeep from 'lodash.clonedeep';
import { In, Repository } from 'typeorm';
import { OncologyConsultationHistoryModel } from '../models/oncology-consultation-history.model';
import { OncologyConsultationRegisterModel } from '../models/oncology-consultation-register.model';
import { OncologyTreatmentPlanModel } from '../models/oncology-treatment-plan.model';
import { OncologyConsultationHistoryResponse } from '../responses/oncology-consultation-history.response';
import {
  NewOncologyChemoDrugSingleInput,
  OncologyChemoDrugSingleInput,
  OncologyConsultationInput,
  OncologyConsultationLinkedRecordType,
} from '../validators/oncology-consultation-history.input';
import { OncologyTreatmentPlanInput } from '../validators/oncology-treatment-plan.input';
import { BillService } from '@clinify/bills/services/bill.service';
import { InvestigationModel } from '@clinify/investigation/models/investigation.model';
import { OncologyChemoDrugModel } from '@clinify/oncology-consultation-history/models/oncology-chemo-drug.model';
import { InvestigationRequestType } from '@clinify/shared/enums/investigation';
import { QuestionOption } from '@clinify/shared/enums/question.enum';
import { UserType } from '@clinify/shared/enums/users';
import { generateOncologyRoaster } from '@clinify/shared/helper/oncology/oncology-roaster';
import {
  FilterInput,
  RecordCreator,
} from '@clinify/shared/validators/filter.input';
import {
  validateMutator,
  validateNumberOfBillsOnRecords,
  validateRecordArchiver,
  validateRecordRemover,
  validateSubRecordCreation,
  validateUpdateBillableRecords,
} from '@clinify/shared/validators/validate-record-mutation.validator';
import {
  OncologyChartType,
  OncologyRegisterChart,
} from '@clinify/users/inputs/oncology-register.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { resolveSubBillRef } from '@clinify/utils/helpers/billing.util';
import { oncologyConsultationLinkedRecordMapper } from '@clinify/utils/link-records/mappers';
import { takePaginatedResponses } from '@clinify/utils/pagination';

export interface IOncologyConsultationHistoryRepository
  extends Repository<OncologyConsultationHistoryModel> {
  this: Repository<OncologyConsultationHistoryModel>;
  findByProfile(
    mutator: ProfileModel,
    profileId: string,
    options: Partial<FilterInput>,
  ): Promise<OncologyConsultationHistoryResponse>;
  getOncologyHistory(
    mutator: ProfileModel,
    oncologyId: string,
  ): Promise<OncologyConsultationHistoryModel>;
  createOncologyRegister(
    mutatorName: string,
    oncologyRecord: OncologyConsultationHistoryModel,
    oncologyOldRecord?: OncologyConsultationHistoryModel,
  ): Promise<OncologyConsultationRegisterModel>;
  updateOncologyHistory(
    mutator: ProfileModel,
    oncologyId: string,
    input: OncologyConsultationInput,
    onlyServiceDetail?: boolean,
  ): Promise<
    [
      OncologyConsultationHistoryModel,
      OncologyConsultationHistoryModel,
      {
        unsavedChemoDrugs: OncologyChemoDrugModel[];
        deletedChemoDrugs: OncologyChemoDrugModel[];
        validChemoDrugs: OncologyChemoDrugModel[];
      },
    ]
  >;
  updateOncologyRegister(
    mutator: ProfileModel,
    id: string,
    chartType: OncologyChartType,
    input: OncologyRegisterChart,
  ): Promise<OncologyConsultationRegisterModel>;
  deleteOncologyHistory(
    mutator: ProfileModel,
    oncologyIds: string[],
  ): Promise<OncologyConsultationHistoryModel[]>;
  archiveOncologyHistory(
    mutator: ProfileModel,
    oncologyIds: string[],
    archive: boolean,
    billService?: BillService,
  ): Promise<OncologyConsultationHistoryModel[]>;
  concealOncologyConsultationFields(
    this: IOncologyConsultationHistoryRepository,
    mutator: ProfileModel,
    oncologyId: string,
    concealStatus: boolean,
    fieldPath:
      | 'concealComplaint'
      | 'concealComplaintHistory'
      | 'concealSystemReview'
      | 'concealPhysicalExam'
      | 'concealAudiometry'
      | 'concealHealthEducation'
      | 'concealChemoNote',
  ): Promise<OncologyConsultationHistoryModel>;
  addChemoDrug(
    mutator: ProfileModel,
    input: NewOncologyChemoDrugSingleInput,
  ): Promise<OncologyChemoDrugModel>;
  updateChemoDrug(
    mutator: ProfileModel,
    input: OncologyChemoDrugSingleInput,
  ): Promise<OncologyChemoDrugModel>;
  deleteChemoDrug(chemoDrugId: string): Promise<OncologyChemoDrugModel>;
  linkRecordsToOncologyConsultation(
    oncologyConsultationHistory: OncologyConsultationHistoryModel,
    recordIds: string[],
    updatedBy: ProfileModel,
    recordType: OncologyConsultationLinkedRecordType,
  ): Promise<OncologyConsultationHistoryModel>;
  getLinkedRadiologyInvestigationRecords(
    profile: ProfileModel,
    oncologyId: string,
  ): Promise<InvestigationModel[]>;
  getLinkedLaboratoryInvestigationRecords(
    profile: ProfileModel,
    oncologyId: string,
  ): Promise<InvestigationModel[]>;
  getLinkedInvestigationRecords(
    profile: ProfileModel,
    oncologyId: string,
  ): Promise<InvestigationModel[]>;
  oncologyConsultationTreatmentPlans(
    profile: ProfileModel,
    consultationId: string,
  ): Promise<OncologyTreatmentPlanModel[]>;
  addOncologyConsultationTreatmentPlan(
    profile: ProfileModel,
    consultationId: string,
    input: OncologyTreatmentPlanInput,
  ): Promise<OncologyTreatmentPlanModel>;
  updateOncologyConsultationTreatmentPlan(
    profile: ProfileModel,
    id: string,
    input: OncologyTreatmentPlanInput,
  ): Promise<OncologyTreatmentPlanModel>;
  deleteOncologyConsultationTreatmentPlan(
    profile: ProfileModel,
    id: string,
  ): Promise<OncologyTreatmentPlanModel>;
  concealOncologyTreatmentPlanFields(
    this: IOncologyConsultationHistoryRepository,
    mutator: ProfileModel,
    treatmentPlanId: string,
    concealStatus: boolean,
    fieldPath: 'conceal' | 'concealObservationNote',
  ): Promise<OncologyTreatmentPlanModel>;
  updateOncologyTreatmentPlanConsentSignature(
    profile: ProfileModel,
    treatmentPlanId: string,
    patientConsentSignature: string,
    patientConsentSignatureType: string,
    shouldSave?: boolean,
  ): Promise<OncologyTreatmentPlanModel>;
}

export const CustomOncologyConsultationHistoryRepoMethods: Pick<
  IOncologyConsultationHistoryRepository,
  | 'findByProfile'
  | 'getOncologyHistory'
  | 'createOncologyRegister'
  | 'updateOncologyHistory'
  | 'updateOncologyRegister'
  | 'deleteOncologyHistory'
  | 'archiveOncologyHistory'
  | 'concealOncologyConsultationFields'
  | 'addChemoDrug'
  | 'updateChemoDrug'
  | 'deleteChemoDrug'
  | 'linkRecordsToOncologyConsultation'
  | 'getLinkedRadiologyInvestigationRecords'
  | 'getLinkedLaboratoryInvestigationRecords'
  | 'getLinkedInvestigationRecords'
  | 'oncologyConsultationTreatmentPlans'
  | 'addOncologyConsultationTreatmentPlan'
  | 'updateOncologyConsultationTreatmentPlan'
  | 'deleteOncologyConsultationTreatmentPlan'
  | 'concealOncologyTreatmentPlanFields'
  | 'updateOncologyTreatmentPlanConsentSignature'
> = {
  async findByProfile(
    mutator: ProfileModel,
    profileId: string,
    filterInput: Partial<FilterInput>,
  ): Promise<OncologyConsultationHistoryResponse> {
    const {
      skip = 0,
      take = 50,
      dateRange,
      keyword,
      creator,
      archive,
    } = {
      ...filterInput,
    };

    let query = this.createQueryBuilder('oncologyHistory')
      .leftJoinAndSelect('oncologyHistory.oncologyRegister', 'oncologyRegister')
      .leftJoinAndSelect('oncologyHistory.profile', 'profile')
      .leftJoinAndSelect('oncologyHistory.hospital', 'hospital')
      .leftJoinAndSelect('oncologyHistory.bill', 'oncologyHistoryBill')
      .where('oncologyHistory.profile = :profileId', { profileId })
      .andWhere('oncologyHistory.archived = :archived', {
        archived: !!archive,
      });

    if (mutator.branchIds?.length)
      query = query.andWhere(
        `((hospital.facility_created_visibility = false OR hospital.facility_created_visibility IS NULL) 
        OR oncologyHistory.hospital IN(:...branchIds))`,
        {
          branchIds: mutator.branchIds,
        },
      );

    if (creator) {
      query = query
        .withDeleted()
        .innerJoinAndSelect('oncologyHistory.createdBy', 'createdBy')
        .andWhere(
          creator === RecordCreator.SELF
            ? 'createdBy.type = :patient'
            : 'createdBy.type != :patient',
          { patient: UserType.Patient },
        );
    }

    if (keyword) {
      query = query.andWhere(
        `(
          oncologyHistory.specialty ILIKE :keyword OR
          oncologyHistory.doctor_name ILIKE :keyword OR
          "oncologyHistoryBill"::text ILIKE :keyword OR
          oncologyHistory.duration ILIKE :keyword OR
          oncologyHistory.facility_name ILIKE :keyword OR
          oncologyHistory.initial_diagnosis_icd10 ILIKE :keyword OR
          oncologyHistory.initial_diagnosis_icd11 ILIKE :keyword OR
          oncologyHistory.initial_diagnosis_snomed ILIKE :keyword OR
          oncologyHistory.final_diagnosis_icd10 ILIKE :keyword OR
          oncologyHistory.final_diagnosis_icd11 ILIKE :keyword OR
          oncologyHistory.final_diagnosis_snomed ILIKE :keyword
        )`, // eslint-disable-line
        {
          keyword: `%${keyword}%`,
        },
      );
    }

    if (dateRange?.from) {
      query = query.andWhere(
        '(COALESCE(oncologyHistory.consultation_date, oncologyHistory.createdDate) >= :from)',
        {
          from: dateRange.from,
        },
      );
    }

    if (dateRange?.to) {
      query = query.andWhere(
        '(COALESCE(oncologyHistory.consultation_date, oncologyHistory.createdDate) < :to)',
        { to: dateRange.to },
      );
    }

    query = query
      .orderBy('oncologyHistory.createdDate', 'DESC')
      .skip(skip)
      .take(take);

    const response = await query.getManyAndCount();

    return new OncologyConsultationHistoryResponse(
      ...takePaginatedResponses(response, take),
    );
  },

  getOncologyHistory(
    mutator: ProfileModel,
    oncologyId: string,
  ): Promise<OncologyConsultationHistoryModel> {
    return this.createQueryBuilder('oncologyHistory')
      .withDeleted()
      .leftJoinAndSelect('oncologyHistory.createdBy', 'createdBy')
      .leftJoinAndSelect('oncologyHistory.profile', 'profile')
      .leftJoinAndSelect('oncologyHistory.oncologyRegister', 'oncologyRegister')
      .leftJoinAndSelect(
        'oncologyHistory.oncologyChemoDrugs',
        'oncologyChemoDrugs',
      )
      .leftJoinAndSelect('oncologyHistory.treatmentPlans', 'treatmentPlans')
      .leftJoinAndSelect('treatmentPlans.createdBy', 'treatmentPlanCreatedBy')
      .where('oncologyHistory.id = :id', { id: oncologyId })
      .getOne();
  },

  async createOncologyRegister(
    this: IOncologyConsultationHistoryRepository,
    mutatorName: string,
    oncologyRecord: OncologyConsultationHistoryModel,
    oncologyOldRecord?: OncologyConsultationHistoryModel,
  ): Promise<OncologyConsultationRegisterModel> {
    const treatmentChart: OncologyRegisterChart = await generateOncologyRoaster(
      'treatment',
      oncologyRecord,
      oncologyOldRecord,
    );
    const therapyChart: OncologyRegisterChart = await generateOncologyRoaster(
      'therapy',
      oncologyRecord,
      oncologyOldRecord,
    );

    return this.manager.save(
      new OncologyConsultationRegisterModel({
        ...oncologyOldRecord?.oncologyRegister,
        treatmentChart,
        therapyChart,
        oncologyHistory: oncologyRecord,
        hospital: oncologyRecord?.hospital,
        ...(oncologyOldRecord
          ? { lastModifierName: mutatorName }
          : { creatorName: mutatorName }),
      }),
    );
  },

  async updateOncologyHistory(
    this: IOncologyConsultationHistoryRepository,
    mutator: ProfileModel,
    oncologyId: string,
    input: OncologyConsultationInput,
    onlyServiceDetail?: boolean,
  ): Promise<
    [
      OncologyConsultationHistoryModel,
      OncologyConsultationHistoryModel,
      {
        unsavedChemoDrugs: OncologyChemoDrugModel[];
        deletedChemoDrugs: OncologyChemoDrugModel[];
        validChemoDrugs: OncologyChemoDrugModel[];
      },
    ]
  > {
    const oncologyHistoryInfo = await this.createQueryBuilder('oncologyHistory')
      .leftJoinAndSelect('oncologyHistory.createdBy', 'createdBy')
      .leftJoinAndSelect('oncologyHistory.profile', 'profile')
      .leftJoinAndSelect('oncologyHistory.oncologyRegister', 'oncologyRegister')
      .leftJoinAndSelect(
        'oncologyHistory.oncologyChemoDrugs',
        'oncologyChemoDrugs',
      )
      .leftJoinAndSelect('oncologyHistory.treatmentPlans', 'treatmentPlans')
      .where('oncologyHistory.id = :id', { id: oncologyId })
      .andWhere('profile.clinifyId = :clinifyId', {
        clinifyId: input?.clinifyId,
      })
      .getOne();

    if (!oncologyHistoryInfo) throw new NotFoundException('Record Not Found');

    validateUpdateBillableRecords(
      mutator,
      oncologyHistoryInfo,
      onlyServiceDetail,
      [
        UserType.OrganizationStaffAdmin,
        UserType.OrganizationDoctor,
        UserType.OrganizationNurse,
      ],
    );

    const unsavedChemoDrugs = input?.oncologyChemoDrugs
      ?.filter((drug) => !drug.id)
      .map(
        (item) =>
          new OncologyChemoDrugModel({
            ...item,
            createdBy: mutator,
            creatorName: mutator.fullName,
            oncologyConsultationHistoryId: oncologyHistoryInfo.id,
          }),
      );
    const deletedChemoDrugs = oncologyHistoryInfo.oncologyChemoDrugs?.filter(
      (drug) => !input?.oncologyChemoDrugs?.find((d) => d.id === drug.id),
    );
    const validChemoDrugs = oncologyHistoryInfo.oncologyChemoDrugs
      ?.filter(
        (drug) => !!input?.oncologyChemoDrugs?.find((d) => d.id === drug.id),
      )
      .map((drug) => {
        const updatedDrug = input?.oncologyChemoDrugs?.find(
          (d) => d.id === drug.id,
        );
        return {
          ...drug,
          ...updatedDrug,
          updatedBy: mutator,
          lastModifierName: mutator.fullName,
        };
      });

    const updateData = { ...oncologyHistoryInfo, ...input };

    const subBillServiceDetail = updateData.serviceDetails?.slice(1);
    const subBillRef = resolveSubBillRef(
      subBillServiceDetail,
      oncologyHistoryInfo,
    );

    if (!!deletedChemoDrugs.length) {
      await this.manager.delete(
        OncologyChemoDrugModel,
        deletedChemoDrugs.map(({ id }) => id),
      );
    }

    const updatedOncologyHistory = await this.save({
      ...updateData,
      oncologyChemoDrugs: [
        ...(validChemoDrugs || []),
        ...(unsavedChemoDrugs || []),
      ],
      subBillRef,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    } as OncologyConsultationHistoryModel);

    return [
      updatedOncologyHistory,
      oncologyHistoryInfo,
      {
        unsavedChemoDrugs,
        deletedChemoDrugs,
        validChemoDrugs,
      },
    ];
  },

  async updateOncologyRegister(
    this: IOncologyConsultationHistoryRepository,
    mutator: ProfileModel,
    id: string,
    chartType: OncologyChartType,
    input: OncologyRegisterChart,
  ): Promise<OncologyConsultationRegisterModel> {
    const info = await this.manager
      .createQueryBuilder(OncologyConsultationRegisterModel, 'oncologyRegister')
      .leftJoinAndSelect('oncologyRegister.oncologyHistory', 'oncologyHistory')
      .leftJoinAndSelect('oncologyHistory.createdBy', 'createdBy')
      .leftJoinAndSelect('oncologyHistory.profile', 'profile')
      .where('oncologyRegister.id = :id', { id })
      .getOne();

    if (!info) throw new NotFoundException('Record Not Found');

    validateMutator(mutator, info.oncologyHistory, [
      UserType.OrganizationStaffAdmin,
    ]);
    return this.manager.save(OncologyConsultationRegisterModel, {
      ...info,
      [`${chartType.toLowerCase()}Chart`]: input,
      lastModifierName: mutator.fullName,
    });
  },

  async deleteOncologyHistory(
    this: IOncologyConsultationHistoryRepository,
    mutator: ProfileModel,
    oncologyIds: string[],
  ): Promise<OncologyConsultationHistoryModel[]> {
    const oncologyHistories = await this.find({
      where: { id: In(oncologyIds) },
      join: {
        alias: 'oncology',
        leftJoinAndSelect: {
          bill: 'oncology.bill',
          receiverProfile: 'bill.receiverProfile',
          billDetails: 'bill.details',
          subBills: 'billDetails.subBills',
          billDetailsCreatedBy: 'billDetails.createdBy',
          createdBy: 'oncology.createdBy',
          oncologyChemoDrugs: 'oncology.oncologyChemoDrugs',
          profile: 'oncology.profile',
        },
      },
    });
    const validResources = validateRecordRemover(
      mutator,
      oncologyHistories,
      undefined,
      true,
    );
    if (validResources.length) {
      await this.remove(
        cloneDeep(validResources).map((v) => ({
          ...v,
          deletedBy: {
            id: mutator.id,
            fullName: mutator.fullName,
            entityId: v.id,
          },
        })),
      );
    }
    return validResources;
  },

  async archiveOncologyHistory(
    this: IOncologyConsultationHistoryRepository,
    mutator: ProfileModel,
    oncologyIds: string[],
    archive: boolean,
    billService?: BillService,
  ): Promise<OncologyConsultationHistoryModel[]> {
    const oncologyHistories = await this.find({
      relations: [
        'createdBy',
        'bill',
        'bill.details',
        'bill.receiverProfile',
        'bill.createdBy',
      ],
      where: { id: In(oncologyIds) },
    });
    let validResources = validateRecordArchiver(mutator, oncologyHistories);
    validResources = validateNumberOfBillsOnRecords(validResources, archive);

    if (!validResources.length) return [];

    const validIds = validResources.map((v) => v.id);
    await this.createQueryBuilder('oncology_histories')
      .update(OncologyConsultationHistoryModel)
      .set({ archived: archive, updatedDate: () => 'updated_date' })
      .whereInIds(validIds)
      .execute();
    const billIds = validResources?.map((resource) => resource?.billId);
    await billService.archiveBillsR(billIds, archive);

    return validResources.map((v) => ({ ...v, archived: archive }));
  },

  async concealOncologyConsultationFields(
    this: IOncologyConsultationHistoryRepository,
    mutator: ProfileModel,
    oncologyId: string,
    concealStatus: boolean,
    fieldPath: string,
  ): Promise<OncologyConsultationHistoryModel> {
    const oncologyHistory = await this.findOneOrFail({
      relations: ['createdBy', 'updatedBy'],
      where: { id: oncologyId },
    }).catch(() => {
      throw new NotFoundException('Record Not Found');
    });

    validateMutator(mutator, oncologyHistory, [
      UserType.OrganizationStaffAdmin,
    ]);

    await this.createQueryBuilder()
      .update(OncologyConsultationHistoryModel)
      .set({
        updatedDate: () => 'updated_date',
        [fieldPath]: concealStatus,
      })
      .where('id = :id', { id: oncologyId })
      .execute();

    return { ...oncologyHistory, [fieldPath]: concealStatus };
  },

  async addChemoDrug(
    this: IOncologyConsultationHistoryRepository,
    mutator: ProfileModel,
    input: NewOncologyChemoDrugSingleInput,
  ): Promise<OncologyChemoDrugModel> {
    const oncology = await this.findOneOrFail({
      where: { id: input.oncologyConsultationHistoryId },
    }).catch(() => {
      throw new NotFoundException('Record Not Found');
    });
    const chemoDrug = new OncologyChemoDrugModel({
      ...input,
      createdBy: mutator,
      creatorName: mutator.fullName,
      oncologyConsultationHistoryId: oncology.id,
    });
    return this.manager.save(OncologyChemoDrugModel, chemoDrug);
  },
  async updateChemoDrug(
    this: IOncologyConsultationHistoryRepository,
    mutator: ProfileModel,
    input: OncologyChemoDrugSingleInput,
  ): Promise<OncologyChemoDrugModel> {
    const chemoDrug = await this.manager
      .findOneOrFail(OncologyChemoDrugModel, {
        where: { id: input.id },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });
    return this.manager.save(OncologyChemoDrugModel, {
      ...chemoDrug,
      ...input,
      updatedBy: mutator,
      lastModifierName: mutator.fullName,
    });
  },
  async deleteChemoDrug(
    this: IOncologyConsultationHistoryRepository,
    chemoDrugId: string,
  ): Promise<OncologyChemoDrugModel> {
    const chemoDrug = await this.manager
      .findOneOrFail(OncologyChemoDrugModel, {
        where: { id: chemoDrugId },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });
    await this.manager.delete(OncologyChemoDrugModel, chemoDrug.id);
    return chemoDrug;
  },

  async linkRecordsToOncologyConsultation(
    this: IOncologyConsultationHistoryRepository,
    oncologyConsultation: OncologyConsultationHistoryModel,
    recordIds: string[],
    updatedBy: ProfileModel,
    recordType: OncologyConsultationLinkedRecordType,
  ): Promise<OncologyConsultationHistoryModel> {
    const { id } = oncologyConsultation;
    const { Model, linkPath, joinTableName } =
      oncologyConsultationLinkedRecordMapper.get(recordType);

    const linkedRecords = await this.manager.find(Model, {
      where: { id: In(recordIds) },
    });
    let sqlValues;

    if (recordType === OncologyConsultationLinkedRecordType.Admission) {
      sqlValues = linkedRecords
        .map((record: any) => `('${record.id}', '${id}')`)
        .join(',');

      await this.manager.query(
        `DELETE FROM "${joinTableName}" WHERE oncology_consultation_id = '${id}'`,
      );
    } else {
      sqlValues = linkedRecords
        .map((record: any) => `('${id}', '${record.id}')`)
        .join(',');

      await this.manager.query(
        `DELETE FROM "${joinTableName}" WHERE oncology_consultation_id = '${id}'`,
      );
    }

    if (linkedRecords.length) {
      await this.manager.query(
        `INSERT INTO "${joinTableName}" VALUES ${sqlValues}`,
      );
    }

    return new OncologyConsultationHistoryModel({
      ...oncologyConsultation,
      [linkPath]: linkedRecords,
    });
  },

  async getLinkedRadiologyInvestigationRecords(
    this: IOncologyConsultationHistoryRepository,
    profile: ProfileModel,
    oncologyId: string,
  ): Promise<InvestigationModel[]> {
    return await this.manager
      .createQueryBuilder(InvestigationModel, 'investigation')
      .innerJoin(
        'investigation.oncology_consultation_investigation',
        'oncology_consultation_investigation',
        'oncology_consultation_investigation.oncologyConsultationId = :id',
        { id: oncologyId },
      )
      .where('investigation.requestType = :type', {
        type: InvestigationRequestType.Radiology,
      })
      .getMany();
  },

  async getLinkedLaboratoryInvestigationRecords(
    this: IOncologyConsultationHistoryRepository,
    profile: ProfileModel,
    oncologyId: string,
  ): Promise<InvestigationModel[]> {
    return await this.manager
      .createQueryBuilder(InvestigationModel, 'investigation')
      .innerJoin(
        'investigation.oncology_consultation_investigation',
        'oncology_consultation_investigation',
        'oncology_consultation_investigation.oncologyConsultationId = :id',
        { id: oncologyId },
      )
      .where('investigation.requestType = :type', {
        type: InvestigationRequestType.Laboratory,
      })
      .getMany();
  },

  async getLinkedInvestigationRecords(
    this: IOncologyConsultationHistoryRepository,
    profile: ProfileModel,
    oncologyId: string,
  ): Promise<InvestigationModel[]> {
    return await this.manager
      .createQueryBuilder(InvestigationModel, 'investigation')
      .innerJoin(
        'investigation.oncology_consultation_investigation',
        'oncology_consultation_investigation',
        'oncology_consultation_investigation.oncologyConsultationId = :id',
        { id: oncologyId },
      )
      .getMany();
  },

  async oncologyConsultationTreatmentPlans(
    this: IOncologyConsultationHistoryRepository,
    profile: ProfileModel,
    consultationId: string,
  ): Promise<OncologyTreatmentPlanModel[]> {
    const oncologyConsultationHistory = await this.findOneOrFail({
      where: {
        id: consultationId,
      },
      relations: ['createdBy'],
    }).catch(() => {
      throw new NotFoundException('Consultation Record Not Found');
    });
    validateSubRecordCreation(profile, oncologyConsultationHistory);

    return this.manager.find(OncologyTreatmentPlanModel, {
      where: {
        oncologyConsultationHistory: { id: oncologyConsultationHistory.id },
      },
    });
  },

  async addOncologyConsultationTreatmentPlan(
    this: IOncologyConsultationHistoryRepository,
    profile: ProfileModel,
    consultationId: string,
    input: OncologyTreatmentPlanInput,
  ): Promise<OncologyTreatmentPlanModel> {
    const oncologyConsultationHistory = await this.findOneOrFail({
      where: { id: consultationId },
      relations: ['createdBy'],
    }).catch(() => {
      throw new NotFoundException('Consultation Record Not Found');
    });

    validateSubRecordCreation(profile, oncologyConsultationHistory);

    return this.manager.save(
      new OncologyTreatmentPlanModel({
        ...input,
        oncologyConsultationHistory,
        createdBy: profile,
        creatorName: profile.fullName,
      }),
    );
  },

  async updateOncologyConsultationTreatmentPlan(
    this: IOncologyConsultationHistoryRepository,
    profile: ProfileModel,
    id: string,
    input: OncologyTreatmentPlanInput,
  ): Promise<OncologyTreatmentPlanModel> {
    const subRecord = await this.manager
      .createQueryBuilder(OncologyTreatmentPlanModel, 'treatment_plan')
      .leftJoinAndSelect('treatment_plan.createdBy', 'createdBy')
      .leftJoinAndSelect(
        'treatment_plan.oncologyConsultationHistory',
        'oncologyConsultationHistory',
      )
      .leftJoinAndSelect(
        'oncologyConsultationHistory.treatmentPlans',
        'treatmentPlans',
      )
      .where('treatment_plan.id = :id', {
        id,
      })
      .getOne();

    if (!subRecord) {
      throw new NotFoundException('Record Not Found');
    }

    validateMutator(profile, subRecord);

    subRecord.updatedBy = profile;
    subRecord.lastModifierName = profile.fullName;
    const updatedRecord = await this.manager.save(OncologyTreatmentPlanModel, {
      ...subRecord,
      ...input,
    });

    return updatedRecord;
  },

  async deleteOncologyConsultationTreatmentPlan(
    this: IOncologyConsultationHistoryRepository,
    profile: ProfileModel,
    id: string,
  ): Promise<OncologyTreatmentPlanModel> {
    const subRecord = await this.manager
      .findOneOrFail(OncologyTreatmentPlanModel, {
        where: {
          id,
        },
        relations: [
          'createdBy',
          'oncologyConsultationHistory',
          'oncologyConsultationHistory.treatmentPlans',
        ],
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    const validResources = validateRecordRemover(profile, [subRecord]);
    if (!validResources.length)
      throw new NotAcceptableException('Record Cannot Be Deleted');

    await this.manager.remove(
      OncologyTreatmentPlanModel,
      cloneDeep(validResources),
    );

    subRecord.updatedBy = profile;
    return subRecord;
  },

  async concealOncologyTreatmentPlanFields(
    this: IOncologyConsultationHistoryRepository,
    mutator: ProfileModel,
    treatmentPlanId: string,
    concealStatus: boolean,
    fieldPath: 'conceal' | 'concealObservationNote',
  ): Promise<OncologyTreatmentPlanModel> {
    const treatmentPlan = await this.manager
      .findOneOrFail(OncologyTreatmentPlanModel, {
        join: {
          alias: 'treatmentPlan',
          leftJoinAndSelect: {
            oncologyConsultationHistory:
              'treatmentPlan.oncologyConsultationHistory',
            createdBy: 'treatmentPlan.createdBy',
            updatedBy: 'treatmentPlan.updatedBy',
          },
        },
        where: { id: treatmentPlanId },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    validateMutator(mutator, treatmentPlan, [UserType.OrganizationStaffAdmin]);

    await this.createQueryBuilder()
      .update(OncologyTreatmentPlanModel)
      .set({ updatedDate: () => 'updated_date', [fieldPath]: concealStatus })
      .where('id = :id', { id: treatmentPlanId })
      .execute();

    return { ...treatmentPlan, [fieldPath]: concealStatus };
  },

  async updateOncologyTreatmentPlanConsentSignature(
    this: IOncologyConsultationHistoryRepository,
    profile: ProfileModel,
    treatmentPlanId: string,
    patientConsentSignature: string,
    patientConsentSignatureType: string,
    shouldSave?: boolean,
  ): Promise<OncologyTreatmentPlanModel> {
    const treatmentPlan = await this.manager
      .findOneOrFail(OncologyTreatmentPlanModel, {
        where: { id: treatmentPlanId },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    const consentDetails = {
      ...(shouldSave ? { admissionConsent: QuestionOption.Yes } : {}),
      patientConsentSignature,
      patientConsentSignatureType,
      patientConsentSignatureDateTime: new Date(),
    };

    await this.createQueryBuilder()
      .update(OncologyTreatmentPlanModel)
      .set({
        ...consentDetails,
        id: treatmentPlanId,
        updatedDate: () => 'updated_date',
      })
      .where('id = :id', { id: treatmentPlanId })
      .execute();

    return { ...treatmentPlan, ...consentDetails };
  },
};
