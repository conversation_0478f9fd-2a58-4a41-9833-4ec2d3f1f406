/* eslint-disable max-lines */
import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Chance } from 'chance';
import moment from 'moment/moment';
import { DataSource, EntityManager } from 'typeorm';
import {
  CustomOncologyConsultationHistoryRepoMethods,
  IOncologyConsultationHistoryRepository,
} from './oncology-consultation-history.repository';
import * as db from '../../database/index';
import { OncologyConsultationHistoryModel } from '../models/oncology-consultation-history.model';
import { treatmentPlanFactory } from '@clinify/__mocks__/factories/consultation.factory';
import { oncologyConsultationFactory } from '@clinify/__mocks__/factories/oncologyConsultation.factory';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import { AllergyModel } from '@clinify/allergies/models/allergy.model';
import { BillService } from '@clinify/bills/services/bill.service';
import { TestDataSourceOptions } from '@clinify/data-source';
import { extendDSRepo } from '@clinify/database/extendModel';
import { HospitalModel } from '@clinify/hospitals/models/hospital.model';
import {
  NewOncologyChemoDrugSingleInput,
  OncologyConsultationLinkedRecordType,
} from '@clinify/oncology-consultation-history/validators/oncology-consultation-history.input';
import { UserType } from '@clinify/shared/enums/users';
import { RecordCreator } from '@clinify/shared/validators/filter.input';
import { OncologyChartType } from '@clinify/users/inputs/oncology-register.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { createAdmissions } from '@clinify/utils/tests/admission.fixtures';
import { createAllergies } from '@clinify/utils/tests/allergy.fixtures';
import { createOncologyConsultations } from '@clinify/utils/tests/oncologyConsultation.fixtures';
import { createHospitals } from '@fixtures/hospital.fixtures';
import { createUsers } from '@fixtures/user.fixtures';

const chance = new Chance();
let billServiceMock: BillService | any;

describe('ConsultationRepository', () => {
  let ds: DataSource;
  let hospitals: HospitalModel[];
  let hospital: HospitalModel;
  let oncologyConsultations: OncologyConsultationHistoryModel[];
  let oncologyConsultation: OncologyConsultationHistoryModel;
  let allergy: AllergyModel;
  let admission: AdmissionModel;
  let profile: ProfileModel;
  let manager: EntityManager;
  let repo: IOncologyConsultationHistoryRepository;
  const spySlaveQuery = jest.spyOn(db, 'queryWithSlave');

  let module: TestingModule;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TypeOrmModule.forRoot(TestDataSourceOptions)],
      providers: [],
    }).compile();

    ds = module.get<DataSource>(DataSource);
    manager = ds.manager;
    repo = extendDSRepo<IOncologyConsultationHistoryRepository>(
      ds,
      OncologyConsultationHistoryModel,
      CustomOncologyConsultationHistoryRepoMethods,
    );

    spySlaveQuery.mockImplementation((qr, pr) => manager.query(qr, pr));
  });

  beforeEach(async () => {
    hospitals = await createHospitals(manager, 2);
    hospital = hospitals[0];
    oncologyConsultations = await createOncologyConsultations(manager, 3);
    oncologyConsultation = oncologyConsultations[0];
    profile = oncologyConsultation.profile;
    [allergy] = await createAllergies(manager, 1, null, null, profile);
    [admission] = await createAdmissions(manager, 1, profile);
    billServiceMock = { archiveBillsR: jest.fn() };
  });

  afterAll(async () => {
    await ds.destroy();
    await module.close();
  });

  it('findByProfile(): should find oncology consultations for a particular user', async () => {
    const { id, specialty, rank: type, createdBy } = oncologyConsultation;
    const record = await repo.findByProfile(createdBy, profile.id, {
      skip: 0,
      take: 50,
    });
    expect(record).toHaveProperty('list');
    expect(record.list).toContainEqual(
      expect.objectContaining({ id, specialty, rank: type, archived: false }),
    );
  });

  it('findByProfile(): should find oncology consultations for a particular user using filters', async () => {
    const { specialty, id, consultationDateTime, createdBy } =
      oncologyConsultation;
    const record = await repo.findByProfile(createdBy, profile.id, {
      skip: 0,
      take: 10,
      keyword: specialty,
      dateRange: {
        from: moment(consultationDateTime).startOf('day').toDate(),
        to: moment(consultationDateTime).endOf('day').toDate(),
      },
    });
    expect(record).toHaveProperty('list');
    expect(record.list).toContainEqual(
      expect.objectContaining({ id, specialty, archived: false }),
    );
  });

  it('findByProfile(): should find oncology consultations for a particular user created by them', async () => {
    const record = await repo.findByProfile(profile, profile.id, {
      creator: RecordCreator.SELF,
    });
    expect(record).toHaveProperty('list');
    expect(
      record.list.every((item) => item.createdBy.id === profile.id),
    ).toBeTruthy();
  });

  it('findByProfile(): should find oncology consultations for a particular user created by others', async () => {
    const record = await repo.findByProfile(profile, profile.id, {
      creator: RecordCreator.OTHERS,
    });
    expect(record).toHaveProperty('list');
    expect(
      record.list.every((item) => item.createdBy.id !== profile.id),
    ).toBeTruthy();
  });

  it('getOncologyHistory(): should get one oncology consultation', async () => {
    const record = await repo.getOncologyHistory(
      profile,
      oncologyConsultation.id,
    );
    expect(record.id).toEqual(oncologyConsultation.id);
    expect(record.specialty).toEqual(oncologyConsultation.specialty);
  });

  it('createOncologyRegister(): should create and save oncology register', async () => {
    const response = await repo.createOncologyRegister(profile.fullName, {
      ...oncologyConsultation,
      treatmentCycleNumber: '5',
      treatmentCycleDays: '5',
    });

    expect(
      oncologyConsultation.oncologyRegister.treatmentChart?.periods,
    ).toBeFalsy();
    expect(response.treatmentChart?.periods?.length).toBe(5);

    const input = { ...response.treatmentChart };
    input.periods[0].cycles[1].value = new Date('2024-05-12');

    const response1 = await repo.updateOncologyRegister(
      profile,
      response.id,
      OncologyChartType.Treatment,
      input,
    );

    expect(response1?.treatmentChart).toStrictEqual(input);
  });

  it('updateOncologyRegister(): should throw error when register Not Found', async () => {
    await expect(
      repo.updateOncologyRegister(
        profile,
        chance.guid({ version: 4 }),
        OncologyChartType.Therapy,
        { ...oncologyConsultation.oncologyRegister.treatmentChart },
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateOncologyHistory(): should throw when oncology consultation Not Found', async () => {
    const input = oncologyConsultationFactory.build();
    input.clinifyId = profile.clinifyId;

    await expect(
      repo.updateOncologyHistory(profile, input.id, input),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateOncologyHistory() should allow patient to update an oncology consultation', async () => {
    const input = oncologyConsultationFactory.build();
    input.clinifyId = profile.clinifyId;

    delete input.profile;
    input.id = oncologyConsultation.id;
    const updatedOncologyConsultation = await repo.updateOncologyHistory(
      profile,
      input.id,
      input,
    );
    expect(updatedOncologyConsultation[0].doctorName).toEqual(input.doctorName);
    expect(updatedOncologyConsultation[0].specialty).toEqual(input.specialty);
    expect(updatedOncologyConsultation[0].consultationDateTime).toEqual(
      input.consultationDateTime,
    );
  });

  it('updateOncologyHistory() should not allow patient to update another patient record', async () => {
    const [consultationForAnotherPatient] = await createOncologyConsultations(
      manager,
      1,
    );
    const input = oncologyConsultationFactory.build();
    input.id = consultationForAnotherPatient.id;
    input.clinifyId = consultationForAnotherPatient.profile.clinifyId;
    delete input.profile;

    await expect(
      repo.updateOncologyHistory(profile, input.id, input),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updateOncologyHistory() should not allow organization doctors to update records for another hospital', async () => {
    const input = oncologyConsultationFactory.build();
    const [hospitalCreator, hospital] = await createHospitals(manager, 2);
    const [doctor] = await createUsers(manager, 1, hospitalCreator);
    const [anotherDoctorInAnotherHospital] = await createUsers(
      manager,
      1,
      hospital,
    );
    const [consultationsByDoctor] = await createOncologyConsultations(
      manager,
      1,
      doctor.defaultProfile,
    );

    input.createdBy = consultationsByDoctor.createdBy;
    input.id = consultationsByDoctor.id;
    input.clinifyId = consultationsByDoctor.profile.clinifyId;
    delete input.profile;

    await expect(
      repo.updateOncologyHistory(
        anotherDoctorInAnotherHospital.defaultProfile,
        input.id,
        input,
      ),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updateOncologyHistory() should not allow organization billing officer to update records', async () => {
    const input = oncologyConsultationFactory.build();
    const [hospitalCreator, hospital] = await createHospitals(manager, 2);
    const [doctor] = await createUsers(manager, 1, hospitalCreator);
    const [billingOfficer] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationBillingOfficer,
    );

    const [consultationsByDoctor] = await createOncologyConsultations(
      manager,
      1,
      doctor.defaultProfile,
    );

    input.createdBy = consultationsByDoctor.createdBy;
    input.id = consultationsByDoctor.id;
    input.clinifyId = consultationsByDoctor.profile.clinifyId;
    delete input.profile;

    await expect(
      repo.updateOncologyHistory(
        billingOfficer.defaultProfile,
        input.id,
        input,
      ),
    ).rejects.toThrow('Not Authorized To Modify This Record');
  });

  it('updateOncologyHistory() should allow organization billing officer to update records', async () => {
    const input = oncologyConsultationFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [billingOfficer] = await createUsers(
      manager,
      1,
      hospital,
      undefined,
      undefined,
      UserType.OrganizationBillingOfficer,
    );

    const [consultationsByDoctor] = await createOncologyConsultations(
      manager,
      1,
      doctor.defaultProfile,
      hospital,
    );

    input.createdBy = consultationsByDoctor.createdBy;
    input.id = consultationsByDoctor.id;
    input.clinifyId = consultationsByDoctor.profile.clinifyId;
    delete input.profile;

    const [response] = await repo.updateOncologyHistory(
      billingOfficer.defaultProfile,
      input.id,
      input,
      true,
    );

    expect(response.doctorName).toEqual(input.doctorName);
    expect(response.facilityName).toEqual(input.facilityName);
    expect(response.specialty).toEqual(input.specialty);
  });

  it('deleteOncologyHistory() should delete oncology consultation', async () => {
    const [record] = await repo.deleteOncologyHistory(profile, [
      oncologyConsultation.id,
    ]);
    expect(record.id).toEqual(oncologyConsultation.id);
    expect(record.specialty).toEqual(oncologyConsultation.specialty);
  });

  it('deleteOncologyHistory() should only delete record they have access to (created by me)', async () => {
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [consultationsByDoctor, nextConsultationByDoctor] =
      await createOncologyConsultations(manager, 2, doctor.defaultProfile);
    const record = await repo.deleteOncologyHistory(profile, [
      oncologyConsultation.id,
      consultationsByDoctor.id,
      nextConsultationByDoctor.id,
    ]);
    expect(record.length).toEqual(1);
  });

  it('deleteOncologyHistory() should only delete record they have access to (created by them/organization)', async () => {
    const fakeConsultation = oncologyConsultationFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [consultationsByDoctor, nextConsultationByDoctor] =
      await createOncologyConsultations(manager, 2, doctor.defaultProfile);
    const record = await repo.deleteOncologyHistory(doctor.defaultProfile, [
      oncologyConsultation.id,
      consultationsByDoctor.id,
      nextConsultationByDoctor.id,
      fakeConsultation.id,
    ]);
    expect(record.length).toEqual(2);
  });

  it('deleteOncologyHistory() should not delete record when in the same hospital and not created by them', async () => {
    const fakeConsultation = oncologyConsultationFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [anotherDoctorInSameHospital] = await createUsers(
      manager,
      2,
      hospital,
    );

    const [consultationsByDoctor, nextConsultationByDoctor] =
      await createOncologyConsultations(manager, 2, doctor.defaultProfile);
    const record = await repo.deleteOncologyHistory(
      anotherDoctorInSameHospital.defaultProfile,
      [
        oncologyConsultation.id,
        consultationsByDoctor.id,
        nextConsultationByDoctor.id,
        fakeConsultation.id,
      ],
    );
    expect(record.length).toEqual(0);
  });

  it('deleteOncologyHistory() should only delete record they have access to (created by them/organization)', async () => {
    const [hospital, clinic] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [anotherDoctorInAnotherHospital] = await createUsers(
      manager,
      1,
      clinic,
    );

    const [consultationsByDoctor] = await createOncologyConsultations(
      manager,
      2,
      doctor.defaultProfile,
    );
    const record = await repo.deleteOncologyHistory(
      anotherDoctorInAnotherHospital.defaultProfile,
      [oncologyConsultation.id, consultationsByDoctor.id],
    );
    expect(record.length).toBeFalsy();
  });

  it('archiveOncologyHistory() should return empty array when none of the id passed is found', async () => {
    const record = await repo.archiveOncologyHistory(
      profile,
      [chance.guid({ version: 4 }), chance.guid({ version: 4 })],
      true,
      billServiceMock,
    );
    expect(record).toStrictEqual([]);
  });

  it('archiveOncologyHistory() should only archive/unarchive record created by me', async () => {
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);
    const [consultationsByDoctor, nextConsultationByDoctor] =
      await createOncologyConsultations(manager, 2, doctor.defaultProfile);
    let record = await repo.archiveOncologyHistory(
      profile,
      [
        oncologyConsultation.id,
        consultationsByDoctor.id,
        nextConsultationByDoctor.id,
      ],
      true,
      billServiceMock,
    );
    expect(record.length).toEqual(1);
    expect(record).toContainEqual(expect.objectContaining({ archived: true }));

    record = await repo.archiveOncologyHistory(
      profile,
      [
        oncologyConsultation.id,
        consultationsByDoctor.id,
        nextConsultationByDoctor.id,
      ],
      false,
      billServiceMock,
    );
    expect(record.length).toEqual(1);
    expect(record).toContainEqual(expect.objectContaining({ archived: false }));
  });

  it('archiveOncologyHistory() should only archive record created by the organization)', async () => {
    const fakeConsultation = oncologyConsultationFactory.build();
    const [hospital] = await createHospitals(manager);
    const [doctor] = await createUsers(manager, 1, hospital);

    const [consultationsByDoctor, nextConsultationByDoctor] =
      await createOncologyConsultations(manager, 2, doctor.defaultProfile);
    let record = await repo.archiveOncologyHistory(
      doctor.defaultProfile,
      [
        oncologyConsultation.id,
        consultationsByDoctor.id,
        nextConsultationByDoctor.id,
        fakeConsultation.id,
      ],
      true,
      billServiceMock,
    );
    expect(record.length).toEqual(2);
    expect(record).toContainEqual(expect.objectContaining({ archived: true }));

    record = await repo.archiveOncologyHistory(
      doctor.defaultProfile,
      [
        oncologyConsultation.id,
        consultationsByDoctor.id,
        nextConsultationByDoctor.id,
        fakeConsultation.id,
      ],
      false,
      billServiceMock,
    );
    expect(record.length).toEqual(2);
    expect(record).toContainEqual(expect.objectContaining({ archived: false }));
  });

  it('findByProfile() should find archived oncology consultations for a particular user using filters', async () => {
    await repo.archiveOncologyHistory(
      profile,
      [oncologyConsultation.id, oncologyConsultations[2].id],
      true,
      billServiceMock,
    );
    const record = await repo.findByProfile(
      oncologyConsultation.createdBy,
      profile.id,
      {
        archive: true,
      },
    );
    expect(record).toHaveProperty('list');
    expect(record.list).toContainEqual(
      expect.objectContaining({ archived: true }),
    );
  });

  it('concealOncologyConsultationFields(): should update conceal status of provided conceal field', async () => {
    const result1 = await repo.getOncologyHistory(
      profile,
      oncologyConsultation.id,
    );
    expect(result1.concealComplaint).toEqual(true);

    await repo.concealOncologyConsultationFields(
      profile,
      oncologyConsultation.id,
      false,
      'concealComplaint',
    );

    const result2 = await repo.getOncologyHistory(
      profile,
      oncologyConsultation.id,
    );
    expect(result2.concealComplaint).toEqual(false);

    await repo.concealOncologyConsultationFields(
      profile,
      oncologyConsultation.id,
      true,
      'concealComplaint',
    );

    const result3 = await repo.getOncologyHistory(
      profile,
      oncologyConsultation.id,
    );
    expect(result3.concealComplaint).toEqual(true);

    await expect(
      repo.concealOncologyConsultationFields(
        profile,
        chance.guid({ version: 4 }),
        true,
        'concealComplaint',
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('addChemoDrug(): should add chemo drug to oncology consultation', async () => {
    const input: NewOncologyChemoDrugSingleInput = {
      day: '1',
      cycleNumber: 1,
      drugName: 'Book',
      dosage: '50ml',
      dosagePercentage: 'full',
      route: 'mouth',
      oncologyConsultationHistoryId: oncologyConsultation.id,
    };
    const result = await repo.addChemoDrug(profile, input);
    expect(result.id).toBeDefined();
    expect(result.drugName).toEqual(input.drugName);
  });

  it('addChemoDrug(): should throw error when oncology consultation Not Found', async () => {
    const input: NewOncologyChemoDrugSingleInput = {
      day: '1',
      cycleNumber: 1,
      drugName: 'Book',
      dosage: '50ml',
      dosagePercentage: 'full',
      route: 'mouth',
      oncologyConsultationHistoryId: chance.guid({ version: 4 }),
    };
    await expect(repo.addChemoDrug(profile, input)).rejects.toThrow(
      'Record Not Found',
    );
  });

  it('updateChemoDrug(): should update chemo drug in oncology consultation', async () => {
    const input: NewOncologyChemoDrugSingleInput = {
      day: '1',
      cycleNumber: 1,
      drugName: 'Book',
      dosage: '50ml',
      dosagePercentage: 'full',
      route: 'mouth',
      oncologyConsultationHistoryId: oncologyConsultation.id,
    };
    const result = await repo.addChemoDrug(profile, input);
    const updatedInput = { ...input, drugName: 'Pencil', id: result.id };
    const updatedResult = await repo.updateChemoDrug(profile, updatedInput);
    expect(updatedResult.drugName).toEqual(updatedInput.drugName);
  });

  it('updateChemoDrug(): should throw error when chemo drug Not Found', async () => {
    const input: NewOncologyChemoDrugSingleInput = {
      day: '1',
      cycleNumber: 1,
      drugName: 'Book',
      dosage: '50ml',
      dosagePercentage: 'full',
      route: 'mouth',
      oncologyConsultationHistoryId: oncologyConsultation.id,
    };
    await repo.addChemoDrug(profile, input);
    const updatedInput = {
      ...input,
      drugName: 'Pencil',
      id: chance.guid({ version: 4 }),
    };
    await expect(repo.updateChemoDrug(profile, updatedInput)).rejects.toThrow(
      'Record Not Found',
    );
  });

  it('deleteChemoDrug(): should delete chemo drug from oncology consultation', async () => {
    const input: NewOncologyChemoDrugSingleInput = {
      day: '1',
      cycleNumber: 1,
      drugName: 'Book',
      dosage: '50ml',
      dosagePercentage: 'full',
      route: 'mouth',
      oncologyConsultationHistoryId: oncologyConsultation.id,
    };
    const result = await repo.addChemoDrug(profile, input);
    const deletedResult = await repo.deleteChemoDrug(result.id);
    expect(deletedResult.id).toEqual(result.id);
  });

  it('deleteChemoDrug(): should throw error when chemo drug Not Found', async () => {
    await expect(
      repo.deleteChemoDrug(chance.guid({ version: 4 } as any)).catch((e) => {
        expect(e.message).toEqual('Record Not Found');
      }),
    );
  });

  it('linkRecordsToOncologyConsultation should link provided Admission record to Consultation record', async () => {
    const linkedConsultationRecord =
      await repo.linkRecordsToOncologyConsultation(
        oncologyConsultation,
        [admission.id],
        profile,
        OncologyConsultationLinkedRecordType.Admission,
      );

    const linkedAdmissionRecordsIds = linkedConsultationRecord.admissions.map(
      (admission) => admission.id,
    );

    expect(linkedAdmissionRecordsIds).toContain(admission.id);
  });

  it('linkRecordsToOncologyConsultation should link provided Allergy record to Consultation record', async () => {
    const linkedConsultationRecord =
      await repo.linkRecordsToOncologyConsultation(
        oncologyConsultation,
        [allergy.id],
        profile,
        OncologyConsultationLinkedRecordType.Allergy,
      );

    const linkedAllergyRecordsIds = linkedConsultationRecord.allergies.map(
      (allergy) => allergy.id,
    );

    expect(linkedAllergyRecordsIds).toContain(allergy.id);
  });

  it('getLinkedRadiologyInvestigationRecords(): should return radiology records linked to this record', async () => {
    const linkedRecords = await repo.getLinkedRadiologyInvestigationRecords(
      profile,
      oncologyConsultation.id,
    );

    expect(linkedRecords.length).toEqual(0);
    expect(linkedRecords[0]?.id).toEqual(
      oncologyConsultation.oncology_consultation_investigation[0]
        ?.investigationId,
    );
  });

  it('getLinkedLaboratoryInvestigationRecords(): should return labResults linked to this record', async () => {
    const linkedRecords = await repo.getLinkedLaboratoryInvestigationRecords(
      profile,
      oncologyConsultation.id,
    );

    expect(linkedRecords.length).toEqual(0);
    expect(linkedRecords[0]?.id).toEqual(
      oncologyConsultation.oncology_consultation_investigation[0]
        ?.investigationId,
    );
  });

  it('getLinkedInvestigationRecords(): should return investigation linked to this record', async () => {
    const linkedRecords = await repo.getLinkedInvestigationRecords(
      profile,
      oncologyConsultation.id,
    );
    expect(linkedRecords.length).toEqual(0);
    expect(linkedRecords[0]?.id).toEqual(
      oncologyConsultation.oncology_consultation_investigation[0]
        ?.investigationId,
    );
  });

  it('oncologyConsultationTreatmentPlans(): should add, fetch, update and delete treatment plan', async () => {
    const treatmentPlan = treatmentPlanFactory.build();
    const { id, profile } = oncologyConsultation;

    const plan = await repo.addOncologyConsultationTreatmentPlan(
      profile,
      id,
      treatmentPlan,
    );
    expect(plan.oncologyConsultationHistoryId).toEqual(id);

    const record = await repo.getOncologyHistory(profile, id);
    expect(record.treatmentPlans.length).toBe(1);

    const plans = await repo.oncologyConsultationTreatmentPlans(profile, id);
    expect(plans[0].id).toBe(plan.id);

    await expect(
      repo.oncologyConsultationTreatmentPlans(
        profile,
        chance.guid({ version: 4 }),
      ),
    ).rejects.toThrow('Consultation Record Not Found');

    const [newUser] = await createUsers(manager);
    await expect(
      repo.oncologyConsultationTreatmentPlans(newUser.defaultProfile, id),
    ).rejects.toThrow('Not Authorized To Create Record');

    const newTreatmentInout = treatmentPlanFactory.build();
    delete newTreatmentInout.createdDate;
    delete newTreatmentInout.updatedDate;
    const updatedPlan = await repo.updateOncologyConsultationTreatmentPlan(
      profile,
      plan.id,
      newTreatmentInout,
    );

    expect(newTreatmentInout.treatmentPlan).toEqual(updatedPlan.treatmentPlan);

    const deletedPlan = await repo.deleteOncologyConsultationTreatmentPlan(
      profile,
      plan.id,
    );
    expect(deletedPlan).toBeTruthy();
  });

  it('addOncologyConsultationTreatmentPlan(): should throw error when oncology consultation is not found', async () => {
    const treatmentPlan = treatmentPlanFactory.build();
    const { profile } = oncologyConsultationFactory.build();
    await expect(
      repo.addOncologyConsultationTreatmentPlan(
        profile,
        chance.guid({ version: 4 }),
        treatmentPlan,
      ),
    ).rejects.toThrow('Consultation Record Not Found');
  });

  it('updateOncologyConsultationTreatmentPlan(): should throw error when treatment plan is not found', async () => {
    const treatmentPlan = treatmentPlanFactory.build();
    const { profile } = oncologyConsultationFactory.build();
    delete treatmentPlan.createdDate;
    delete treatmentPlan.updatedDate;
    await expect(
      repo.updateOncologyConsultationTreatmentPlan(
        profile,
        chance.guid({ version: 4 }),
        treatmentPlan,
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('deleteOncologyConsultationTreatmentPlan(): should throw error when treatment plan is not found', async () => {
    const { profile } = oncologyConsultationFactory.build();
    await expect(
      repo.deleteOncologyConsultationTreatmentPlan(
        profile,
        chance.guid({ version: 4 }),
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('deleteOncologyConsultationTreatmentPlan(): should throw error if not deleted by the person that created it', async () => {
    const treatmentPlan = treatmentPlanFactory.build();
    const { id, profile } = oncologyConsultation;
    const plan = await repo.addOncologyConsultationTreatmentPlan(
      profile,
      id,
      treatmentPlan,
    );
    expect(plan.oncologyConsultationHistoryId).toEqual(id);

    const [newUser] = await createUsers(manager);
    await expect(
      repo.deleteOncologyConsultationTreatmentPlan(
        newUser.defaultProfile,
        plan.id,
      ),
    ).rejects.toThrow('Record Cannot Be Deleted');
  });

  it('concealOncologyTreatmentPlanFields(): should conceal treatment plan from patient', async () => {
    await expect(
      repo.concealOncologyTreatmentPlanFields(
        profile,
        chance.guid({ version: 4 }),
        true,
        'conceal',
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('concealOncologyTreatmentPlanFields(): should conceal treatment plan from patient', async () => {
    const treatmentPlan = treatmentPlanFactory.build();
    const { id, profile } = oncologyConsultation;
    const plan = await repo.addOncologyConsultationTreatmentPlan(
      profile,
      id,
      treatmentPlan,
    );

    const modTreatmentPlan = await repo.concealOncologyTreatmentPlanFields(
      profile,
      plan.id,
      false,
      'conceal',
    );

    expect(modTreatmentPlan.conceal).toBeFalsy();

    const modTreatmentPlan1 = await repo.concealOncologyTreatmentPlanFields(
      profile,
      plan.id,
      true,
      'conceal',
    );

    expect(modTreatmentPlan1.conceal).toBeTruthy();
  });

  it('concealOncologyTreatmentPlanFields() should conceal treatment plan from patient', async () => {
    const treatmentPlan = treatmentPlanFactory.build();
    const { id, profile } = oncologyConsultation;
    const plan = await repo.addOncologyConsultationTreatmentPlan(
      profile,
      id,
      treatmentPlan,
    );

    const modTreatmentPlan = await repo.concealOncologyTreatmentPlanFields(
      profile,
      plan.id,
      false,
      'concealObservationNote',
    );

    expect(modTreatmentPlan.concealObservationNote).toBeFalsy();

    const modTreatmentPlan1 = await repo.concealOncologyTreatmentPlanFields(
      profile,
      plan.id,
      true,
      'concealObservationNote',
    );

    expect(modTreatmentPlan1.concealObservationNote).toBeTruthy();
  });

  it('updateOncologyTreatmentPlanConsentSignature(): throw error when treatment plan id provided is not found', async () => {
    await expect(
      repo.updateOncologyTreatmentPlanConsentSignature(
        oncologyConsultation.profile,
        chance.guid({ version: 4 }),
        chance.name(),
        'typed',
      ),
    ).rejects.toThrow('Record Not Found');
  });

  it('updateOncologyTreatmentPlanConsentSignature(): should update signature', async () => {
    const treatmentPlanInput = treatmentPlanFactory.build();
    const treatmentPlan = await repo.addOncologyConsultationTreatmentPlan(
      profile,
      oncologyConsultation.id,
      { ...treatmentPlanInput, admissionConsent: 'No' },
    );
    expect(treatmentPlan.admissionConsent).not.toBe('Yes');

    const res = await repo.updateOncologyTreatmentPlanConsentSignature(
      oncologyConsultation.profile,
      treatmentPlan.id,
      'John Doe',
      'typed',
      true,
    );

    expect(res.admissionConsent).toBe('Yes');
    expect(res.patientConsentSignature).toBe('John Doe');
    expect(res.patientConsentSignatureType).toBe('typed');

    const resp = await repo.updateOncologyTreatmentPlanConsentSignature(
      oncologyConsultation.profile,
      treatmentPlan.id,
      'signature-upload-url',
      'upload',
    );

    expect(resp.patientConsentSignature).toBe('signature-upload-url');
    expect(resp.patientConsentSignatureType).toBe('upload');
  });
});
