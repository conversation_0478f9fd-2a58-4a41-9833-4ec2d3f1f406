/* eslint-disable @typescript-eslint/adjacent-overload-signatures */
/* eslint-disable  max-lines */
import { Inject, UseGuards } from '@nestjs/common';
import {
  Args,
  Mutation,
  Parent,
  Query,
  ResolveField,
  Resolver,
  Subscription,
} from '@nestjs/graphql';
import { InjectRepository } from '@nestjs/typeorm';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { EntityManager } from 'typeorm';
import { OncologyConsultationHistoryModel } from '../models/oncology-consultation-history.model';
import { OncologyConsultationRegisterModel } from '../models/oncology-consultation-register.model';
import { OncologyTreatmentPlanModel } from '../models/oncology-treatment-plan.model';
import { OncologyConsultationHistoryService } from '../services/oncology-consultation-history.service';
import {
  NewOncologyChemoDrugSingleInput,
  NewOncologyConsultationInput,
  OncologyChemoCommentInput,
  OncologyChemoDrugSingleInput,
  OncologyConsultationInput,
  OncologyConsultationLinkedRecordType,
} from '../validators/oncology-consultation-history.input';
import { OncologyTreatmentPlanInput } from '../validators/oncology-treatment-plan.input';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import { IAdmissionRepository } from '@clinify/admissions/repositories/admission.repository';
import { SignatureInput } from '@clinify/admissions/validators/admission.input';
import { AllergyModel } from '@clinify/allergies/models/allergy.model';
import { IAllergyRepository } from '@clinify/allergies/repositories/allergy.repository';
import { OrganisationAppointmentModel } from '@clinify/appointments/models/organisation_appointment.model';
import { IOrganisationAppointmentRepository } from '@clinify/appointments/repositories/organisation_appointment.repository';
import { GqlAuthGuard } from '@clinify/authentication/guards/gql.auth.guard';
import { PinAuthGuard } from '@clinify/authentication/guards/pin.guard';
import { ProfileDataAccessGuard } from '@clinify/authentication/guards/profile-data-acess.guard';
import { Permissions } from '@clinify/authorization/decorators/top-level-permissions.decorator';
import { AuthorizationGuard } from '@clinify/authorization/guards/authorization.guard';
import {
  OrganizationAction,
  PatientAction,
  Subject,
} from '@clinify/authorization/types/permission.type';
import { BillModel } from '@clinify/bills/models/bill.model';
import { HmoClaimModel } from '@clinify/hmo-claims/models/hmo-claim.model';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { InvestigationModel } from '@clinify/investigation/models/investigation.model';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { IMedicationRepository } from '@clinify/medications/repositories/medication.repository';
import { NotificationsService } from '@clinify/notifications/services/notifications.service';
import { NursingServiceModel } from '@clinify/nursing-services/models/nursing-services.model';
import { INursingServicesRepository } from '@clinify/nursing-services/repositories/nursing-services.repository';
import { OncologyChemoDrugModel } from '@clinify/oncology-consultation-history/models/oncology-chemo-drug.model';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { PreauthorizationDetailsService } from '@clinify/preauthorization-details/services/preauthorization-details.service';
import { CurrentProfile } from '@clinify/shared/decorators/decorators';
import { LogService } from '@clinify/shared/decorators/logMeta.decorator';
import { BillableRecords } from '@clinify/shared/enums/billable-records';
import { EventType } from '@clinify/shared/enums/events';
import {
  DashboardIcon,
  NotificationTag,
} from '@clinify/shared/enums/notifications';
import { AppServices } from '@clinify/shared/enums/services';
import { concealRecordField } from '@clinify/shared/helper';
import { resolveBillForRecordDelete } from '@clinify/shared/helper/billing';
import { SurgeryModel } from '@clinify/surgeries/models/surgery.model';
import { ISurgeryRepository } from '@clinify/surgeries/repositories/surgery.repository';
import {
  OncologyChartType,
  OncologyRegisterChart,
} from '@clinify/users/inputs/oncology-register.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import {
  filterOncologyConsultationAdded,
  filterOncologyConsultationUpdated,
  filterOncologyConsultationRemoved,
  filterOncologyConsultationArchived,
  filterOncologyConsultationUnarchived,
  filterOncologyConsultationChartUpdated,
  filterChemoDrugUpdated,
  filterOncologyConsultationTreatmentPlanAdded,
  filterOncologyConsultationTreatmentPlanUpdated,
  filterOncologyConsultationTreatmentPlanRemoved,
} from '@clinify/utils/subscriptions/filters';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';
import { VitalModel } from '@clinify/vitals/models/vital.model';
import { IVitalRepository } from '@clinify/vitals/repositories/vital.repository';

const {
  OncologyConsultationAdded,
  OncologyConsultationUpdated,
  OncologyConsultationRemoved,
  OncologyConsultationArchived,
  OncologyConsultationUnarchived,
  OncologyConsultationChartUpdated,
  ChemoDrugUpdated,
  ConsultationEvent,
  OrgBillAdded,
  OrgBillUpdated,
  OrgBillRemoved,
  OrgBillArchived,
  OrgBillUnarchived,
  BillingEvent,
  MedicationEvent,
  OncologyConsultationTreatmentPlanAdded,
  OncologyConsultationTreatmentPlanUpdated,
  OncologyConsultationTreatmentPlanRemoved,
} = SubscriptionTypes;

@LogService(AppServices.OncologyConsultationHistory)
@UseGuards(GqlAuthGuard, AuthorizationGuard)
@Resolver(() => OncologyConsultationHistoryModel)
export class OncologyConsultationHistoryResolver {
  constructor(
    private readonly service: OncologyConsultationHistoryService,
    private readonly notificationService: NotificationsService,
    private readonly preauthDetailsService: PreauthorizationDetailsService,
    private readonly hmoClaimService: HmoClaimService,
    @InjectRepository(VitalModel)
    private vitalRepository: IVitalRepository,
    @InjectRepository(AllergyModel)
    private allergyRepository: IAllergyRepository,
    @InjectRepository(MedicationModel)
    private medicationRepository: IMedicationRepository,
    @InjectRepository(SurgeryModel)
    private surgeryRepository: ISurgeryRepository,
    @InjectRepository(AdmissionModel)
    private admissionRepository: IAdmissionRepository,
    @InjectRepository(NursingServiceModel)
    private nursingServiceRepository: INursingServicesRepository,
    @InjectRepository(OrganisationAppointmentModel)
    private organisationAppointmentRepository: IOrganisationAppointmentRepository,
    private manager: EntityManager,
    @Inject(PUB_SUB) private readonly pubSub: RedisPubSub,
  ) {}

  @Permissions(
    { action: PatientAction.ReadOwn, subject: Subject.ConsultationOncology },
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.ReadTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @UseGuards(ProfileDataAccessGuard('clinifyId'))
  @Query(() => OncologyConsultationHistoryModel)
  async oncologyConsultationHistory(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') oncologyId: string,
    @Args('clinifyId') _clinifyId: string,
  ): Promise<OncologyConsultationHistoryModel> {
    return this.service.getOncologyHistory(profile, oncologyId);
  }

  @Permissions(
    {
      action: PatientAction.CreateOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @UseGuards(PinAuthGuard, ProfileDataAccessGuard('input.clinifyId'))
  @Mutation(() => OncologyConsultationHistoryModel)
  async addOncologyConsultationHistory(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: NewOncologyConsultationInput,
    @Args({ name: 'id', nullable: true }) id?: string,
    @Args({ name: 'prescribe', nullable: true, type: () => Boolean })
    prescribe?: boolean,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<OncologyConsultationHistoryModel> {
    const item = await this.service.saveOncologyHistory(
      profile,
      {
        ...input,
        ...(id && { id }),
      },
      prescribe,
    );
    const details = {
      modelName: DashboardIcon.OncologyConsultationHistory,
      action: NotificationTag.Added,
      item,
    };
    this.notificationService.handleNoticationEvent({ profile, details });

    this.pubSub.publish(ConsultationEvent, {
      consultation: item,
      [ConsultationEvent]: item?.creatorId,
    });

    await this.pubSub.publish(OncologyConsultationAdded, {
      [OncologyConsultationAdded]: item,
    });

    if (!!item.bill) {
      const details = {
        modelName: DashboardIcon.Billing,
        action: NotificationTag.Raised,
        item: item.bill,
      };
      this.notificationService.handleNoticationEvent({
        profile,
        details,
      });

      await this.pubSub.publish(OrgBillAdded, {
        [OrgBillAdded]: item.bill,
      });
      await this.pubSub.publish(BillingEvent, {
        billing: item,
        [BillingEvent]: EventType.ADDED,
      });
    }

    if (item.hmoClaim && !item.hmoClaim.lastModifierId) {
      this.hmoClaimService.handleAddClaimSubscription(item.hmoClaim);
    } else if (item.hmoClaim && item.hmoClaim.lastModifierId) {
      this.hmoClaimService.handleUpdateHmoClaimSubscription(item.hmoClaim);
    }

    return item;
  }

  @Subscription(() => OncologyConsultationHistoryModel, {
    name: OncologyConsultationAdded,
    filter: filterOncologyConsultationAdded,
  })
  addOncologySubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(OncologyConsultationAdded);
  }

  @Permissions(
    {
      action: PatientAction.UpdateOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @UseGuards(PinAuthGuard, ProfileDataAccessGuard('input.clinifyId'))
  @Mutation(() => OncologyConsultationHistoryModel)
  async updateOncologyConsultationHistory(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') oncologyId: string,
    @Args('input') input: OncologyConsultationInput,
    @Args('prescribe', { nullable: true }) prescribe?: boolean,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<OncologyConsultationHistoryModel> {
    const item = await this.service.updateOncologyHistory(
      profile,
      oncologyId,
      input,
      false,
      prescribe,
    );
    const details = {
      modelName: DashboardIcon.OncologyConsultationHistory,
      action: NotificationTag.Updated,
      item,
    };
    this.notificationService.handleNoticationEvent({ profile, details });

    await this.pubSub.publish(ConsultationEvent, {
      consultation: item,
      [ConsultationEvent]: EventType.UPDATED,
    });

    await this.pubSub.publish(OncologyConsultationUpdated, {
      [OncologyConsultationUpdated]: item,
    });

    if (item.bill) {
      await this.pubSub.publish(OrgBillUpdated, {
        [OrgBillUpdated]: item.bill,
      });
    }
    return item;
  }

  @Subscription(() => OncologyConsultationHistoryModel, {
    name: OncologyConsultationUpdated,
    filter: filterOncologyConsultationUpdated,
  })
  updateOncologySubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(OncologyConsultationUpdated);
  }

  @Permissions(
    { action: PatientAction.UpdateOwn, subject: Subject.ConsultationOncology },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
    { action: OrganizationAction.UpdateTeamAny, subject: Subject.RecordBill },
  )
  @UseGuards(ProfileDataAccessGuard('input.clinifyId'))
  @Mutation(() => OncologyConsultationHistoryModel)
  async updateOncologyConsultationHistoryBill(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') oncologyId: string,
    @Args('input') input: OncologyConsultationInput,
  ): Promise<OncologyConsultationHistoryModel> {
    const { clinifyId, serviceDetails } = input;
    const item = await this.service.updateOncologyHistory(
      profile,
      oncologyId,
      { id: oncologyId, clinifyId, serviceDetails },
      true,
    );
    const details = {
      modelName: DashboardIcon.OncologyConsultationHistory,
      action: NotificationTag.Updated,
      item,
    };
    this.notificationService.handleNoticationEvent({ profile, details });

    await this.pubSub.publish(OncologyConsultationUpdated, {
      [OncologyConsultationUpdated]: item,
    });

    if (item.bill) {
      await this.pubSub.publish(OrgBillUpdated, {
        [OrgBillUpdated]: item.bill,
      });
    }
    return item;
  }

  @Permissions(
    {
      action: PatientAction.DeleteOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @UseGuards(ProfileDataAccessGuard('clinifyId'))
  @Mutation(() => [OncologyConsultationHistoryModel])
  async deleteOncologyConsultationHistory(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) oncologyIds: string[],
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    _clinifyId: string,
  ): Promise<OncologyConsultationHistoryModel[]> {
    const items = await this.service.deleteOncologyHistory(
      profile,
      oncologyIds,
    );
    const details = {
      modelName: DashboardIcon.OncologyConsultationHistory,
      action: NotificationTag.Deleted,
      item: items,
    };
    this.notificationService.handleNoticationEvent({ profile, details });

    await this.pubSub.publish(ConsultationEvent, {
      consultation: items[0],
      [ConsultationEvent]: EventType.DELETED,
    });
    await this.pubSub.publish(OncologyConsultationRemoved, {
      [OncologyConsultationRemoved]: items,
    });

    if (items?.length) {
      items.map(async (item) => {
        let resolvedBill;
        const references: string[] = [];
        if (!item.bill) return;

        if (item.bill.details?.length) {
          references.push(item.id);
          item.subBillRef ? references.push(item.subBillRef) : null;
          resolvedBill = resolveBillForRecordDelete(references, {
            ...item.bill,
            updatedBy: profile,
            senderHospital: profile.hospital,
          });
        }

        if (resolvedBill?.details.length) {
          await this.pubSub.publish(OrgBillUpdated, {
            [OrgBillUpdated]: resolvedBill,
          });
        } else {
          await this.pubSub.publish(OrgBillRemoved, {
            [OrgBillRemoved]: [item.bill],
          });
        }
      });
    }

    return items;
  }

  @Subscription(() => OncologyConsultationHistoryModel, {
    name: OncologyConsultationRemoved,
    filter: filterOncologyConsultationRemoved,
  })
  removeOncologySubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(OncologyConsultationRemoved);
  }

  @Permissions(
    {
      action: PatientAction.UpdateOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @UseGuards(PinAuthGuard, ProfileDataAccessGuard('clinifyId'))
  @Mutation(() => OncologyConsultationRegisterModel)
  async updateOncologyConsultationRegister(
    @CurrentProfile() profile: ProfileModel,
    @Args('id') oncologyRegisterId: string,
    @Args('chartType') chartType: OncologyChartType,
    @Args('input') input: OncologyRegisterChart,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    _clinifyId: string,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<OncologyConsultationRegisterModel> {
    const item = await this.service.updateOncologyRegister(
      profile,
      oncologyRegisterId,
      chartType,
      input,
    );
    await this.pubSub.publish(OncologyConsultationChartUpdated, {
      [OncologyConsultationChartUpdated]: item,
    });
    return item;
  }

  @Subscription(() => OncologyConsultationRegisterModel, {
    name: OncologyConsultationChartUpdated,
    filter: filterOncologyConsultationChartUpdated,
  })
  updateOncologyChartSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(OncologyConsultationChartUpdated);
  }

  @Permissions(
    { action: PatientAction.ArchiveOwn, subject: Subject.ConsultationOncology },
    {
      action: OrganizationAction.ArchiveTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.ArchiveTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @UseGuards(ProfileDataAccessGuard('clinifyId'))
  @Mutation(() => [OncologyConsultationHistoryModel])
  async archiveOncologyConsultationHistory(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'ids', type: () => [String] }) oncologyIds: string[],
    @Args({
      name: 'archive',
      type: () => Boolean,
      defaultValue: true,
    })
    archive: boolean,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    _clinifyId: string,
  ): Promise<OncologyConsultationHistoryModel[]> {
    const items = await this.service.archiveOncologyHistory(
      profile,
      oncologyIds,
      archive,
    );
    const pubSubPath = archive
      ? OncologyConsultationArchived
      : OncologyConsultationUnarchived;
    const billPubSubPath = archive ? OrgBillArchived : OrgBillUnarchived;

    await this.pubSub.publish(billPubSubPath, {
      [billPubSubPath]: items.map((item) => item?.bill),
    });
    await this.pubSub.publish(pubSubPath, { [pubSubPath]: items });

    return items;
  }

  @Subscription(() => [OncologyConsultationHistoryModel], {
    name: OncologyConsultationArchived,
    filter: filterOncologyConsultationArchived,
  })
  archiveOncologySubsHandler(@Args('profileId') _profileId: string): any {
    return this.pubSub.asyncIterator(OncologyConsultationArchived);
  }

  @Subscription(() => [OncologyConsultationHistoryModel], {
    name: OncologyConsultationUnarchived,
    filter: filterOncologyConsultationUnarchived,
  })
  unarchiveOncologySubsHandler(@Args('profileId') _profileId: string): any {
    return this.pubSub.asyncIterator(OncologyConsultationUnarchived);
  }

  @ResolveField('bill', () => BillModel, { nullable: true })
  getBill(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): BillModel {
    return profile.hospitalId === root.createdBy?.hospitalId ||
      profile.id === root.profileId
      ? root.bill
      : null;
  }

  @ResolveField(() => PreauthorizationDetailsModel, {
    nullable: true,
    name: 'preauthorizationDetails',
  })
  async getPreauthorizationDetails(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): Promise<PreauthorizationDetailsModel> {
    if (profile.hospitalId !== root.hospitalId) return null;
    const [preauthorizationDetails] =
      await this.preauthDetailsService.getPreauthorizationDetails(
        BillableRecords.OncologyConsultationHistory,
        root.id,
      );

    return preauthorizationDetails;
  }

  @ResolveField(() => HmoClaimModel, { name: 'hmoClaim', nullable: true })
  getHmoClaim(
    @CurrentProfile() mutator: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): Promise<HmoClaimModel> {
    if (mutator.hospitalId !== root.hospitalId || !root.hmoClaimId) return null;
    return this.hmoClaimService.getHmoClaim(mutator, root.hmoClaimId);
  }

  @ResolveField('appointment', () => OrganisationAppointmentModel, {
    nullable: true,
  })
  async getAppointment(
    @Parent() root: OncologyConsultationHistoryModel,
  ): Promise<OrganisationAppointmentModel> {
    if (root.appointmentId) {
      const orgAppointment = await this.manager
        .withRepository(this.organisationAppointmentRepository)
        .findOne({ where: { id: root.appointmentId } });
      return orgAppointment;
    }
    return null;
  }

  @ResolveField('complaint', () => String, { nullable: true })
  getComplaint(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): string {
    return concealRecordField(profile, root) && root.concealComplaint
      ? null
      : root.complaint;
  }

  @ResolveField('complaintHistory', () => String, { nullable: true })
  getComplaintHistory(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): string {
    return concealRecordField(profile, root) && root.concealComplaintHistory
      ? null
      : root.complaintHistory;
  }

  @ResolveField('systemReview', () => String, { nullable: true })
  getSystemReview(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): string {
    return concealRecordField(profile, root) && root.concealSystemReview
      ? null
      : root.systemReview;
  }

  @ResolveField('physicalExam', () => String, { nullable: true })
  getPhysicalExam(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): string {
    return concealRecordField(profile, root) && root.concealPhysicalExam
      ? null
      : root.physicalExam;
  }

  @ResolveField('audiometry', () => String, { nullable: true })
  getAudiometry(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): string {
    return concealRecordField(profile, root) && root.concealAudiometry
      ? null
      : root?.audiometry;
  }

  @ResolveField('healthEducation', () => String, { nullable: true })
  getHealthEducation(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): string {
    return concealRecordField(profile, root) && root.concealHealthEducation
      ? null
      : root?.healthEducation;
  }

  @ResolveField('chemoNote', () => String, { nullable: true })
  getChemoNote(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): string {
    return concealRecordField(profile, root) && root.concealChemoNote
      ? null
      : root?.chemoNote;
  }

  @Permissions(
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyConsultationHistoryModel)
  async concealOncologyConsultationComplaint(
    @CurrentProfile() profile: ProfileModel,
    @Args('oncologyId') oncologyId: string,
    @Args('concealStatus', { type: () => Boolean }) concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const item = await this.service.concealConsultationComplaint(
      profile,
      oncologyId,
      concealStatus,
    );

    await this.pubSub.publish(OncologyConsultationUpdated, {
      [OncologyConsultationUpdated]: item,
    });

    return item;
  }

  @Permissions(
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyConsultationHistoryModel)
  async concealOncologyConsultationComplaintHistory(
    @CurrentProfile() profile: ProfileModel,
    @Args('oncologyId') oncologyId: string,
    @Args('concealStatus', { type: () => Boolean }) concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const item = await this.service.concealConsultationComplaintHistory(
      profile,
      oncologyId,
      concealStatus,
    );

    await this.pubSub.publish(OncologyConsultationUpdated, {
      [OncologyConsultationUpdated]: item,
    });

    return item;
  }

  @Permissions(
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyConsultationHistoryModel)
  async concealOncologyConsultationSystemReview(
    @CurrentProfile() profile: ProfileModel,
    @Args('oncologyId') oncologyId: string,
    @Args('concealStatus', { type: () => Boolean }) concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const item = await this.service.concealConsultationSystemReview(
      profile,
      oncologyId,
      concealStatus,
    );

    await this.pubSub.publish(OncologyConsultationUpdated, {
      [OncologyConsultationUpdated]: item,
    });

    return item;
  }

  @Permissions(
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyConsultationHistoryModel)
  async concealOncologyConsultationPhysicalExam(
    @CurrentProfile() profile: ProfileModel,
    @Args('oncologyId') oncologyId: string,
    @Args('concealStatus', { type: () => Boolean }) concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const item = await this.service.concealConsultationPhysicalExam(
      profile,
      oncologyId,
      concealStatus,
    );

    await this.pubSub.publish(OncologyConsultationUpdated, {
      [OncologyConsultationUpdated]: item,
    });

    return item;
  }

  @Permissions(
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyConsultationHistoryModel)
  async concealOncologyConsultationAudiometry(
    @CurrentProfile() profile: ProfileModel,
    @Args('oncologyId') oncologyId: string,
    @Args('concealStatus') concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const item = await this.service.concealConsultationAudiometry(
      profile,
      oncologyId,
      concealStatus,
    );

    await this.pubSub.publish(OncologyConsultationUpdated, {
      [OncologyConsultationUpdated]: item,
    });

    return item;
  }

  @Permissions(
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyConsultationHistoryModel)
  async concealOncologyConsultationHealthEducation(
    @CurrentProfile() profile: ProfileModel,
    @Args('oncologyId') oncologyId: string,
    @Args('concealStatus', { type: () => Boolean }) concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const item = await this.service.concealConsultationHealthEducation(
      profile,
      oncologyId,
      concealStatus,
    );

    await this.pubSub.publish(OncologyConsultationUpdated, {
      [OncologyConsultationUpdated]: item,
    });

    return item;
  }

  @Permissions(
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyConsultationHistoryModel)
  async concealOncologyConsultationChemoNote(
    @CurrentProfile() profile: ProfileModel,
    @Args('oncologyId') oncologyId: string,
    @Args('concealStatus', { type: () => Boolean }) concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const item = await this.service.concealOncologyConsultationChemoNote(
      profile,
      oncologyId,
      concealStatus,
    );

    await this.pubSub.publish(OncologyConsultationUpdated, {
      [OncologyConsultationUpdated]: item,
    });

    return item;
  }

  @Permissions(
    {
      action: PatientAction.UpdateOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyChemoDrugModel)
  async addChemoDrug(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: NewOncologyChemoDrugSingleInput,
  ): Promise<OncologyChemoDrugModel> {
    return this.service.addChemoDrug(profile, input);
  }

  @Permissions(
    {
      action: PatientAction.UpdateOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyChemoDrugModel)
  async updateChemoDrug(
    @CurrentProfile() profile: ProfileModel,
    @Args('input') input: OncologyChemoDrugSingleInput,
  ): Promise<OncologyChemoDrugModel> {
    return this.service.updateChemoDrug(profile, input);
  }

  @Permissions(
    {
      action: PatientAction.DeleteOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyChemoDrugModel)
  async deleteChemoDrug(
    @Args('id') id: string,
  ): Promise<OncologyChemoDrugModel> {
    return this.service.deleteChemoDrug(id);
  }

  @Permissions(
    {
      action: PatientAction.UpdateOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @UseGuards(PinAuthGuard)
  @Mutation(() => OncologyChemoDrugModel)
  async administerChemoDrug(
    @CurrentProfile() mutator: ProfileModel,
    @Args('id') id: string,
    @Args('period') period: string,
    @Args('status', { type: () => Boolean }) status: boolean,
    @Args({ name: 'clinifyId', type: () => String }) clinifyId: string,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<OncologyChemoDrugModel> {
    const item = await this.service.administerChemoDrug(
      mutator,
      id,
      period,
      status,
    );

    const { oncologyConsultationHistory, ...rest } = item;

    await this.pubSub.publish(ConsultationEvent, {
      consultation: rest,
      [ConsultationEvent]: EventType.UPDATED,
    });
    await this.pubSub.publish(ChemoDrugUpdated, {
      profileId: oncologyConsultationHistory.profileId,
      [ChemoDrugUpdated]: rest,
    });

    if (rest.medicationDetailsId) {
      await this.pubSub.publish(MedicationEvent, {
        medication: { profile: { clinifyId } },
        [MedicationEvent]: EventType.UPDATED,
      });
    }

    return rest;
  }

  @Subscription(() => OncologyChemoDrugModel, {
    name: ChemoDrugUpdated,
    filter: filterChemoDrugUpdated,
  })
  updateChemoDrugSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(ChemoDrugUpdated);
  }

  @Mutation(() => OncologyConsultationHistoryModel)
  async linkRecordsToOncologyConsultation(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'id',
      type: () => String,
      description: 'Oncology Consultation Record ID',
    })
    id: string,
    @Args({
      name: 'recordType',
      type: () => OncologyConsultationLinkedRecordType,
      description: 'Type of Record to Link with Oncology Consultation',
    })
    recordType: OncologyConsultationLinkedRecordType,
    @Args({ name: 'recordIds', type: () => [String] })
    recordIds: string[],
  ): Promise<OncologyConsultationHistoryModel> {
    const item = await this.service.linkRecordsToOncologyConsultation(
      profile,
      id,
      recordType,
      recordIds,
    );

    await this.pubSub.publish(ConsultationEvent, {
      consultation: item,
      [ConsultationEvent]: EventType.UPDATED,
    });
    await this.pubSub.publish(OncologyConsultationUpdated, {
      [OncologyConsultationUpdated]: item,
    });

    return item;
  }

  @ResolveField('admissions', () => [AdmissionModel])
  async getAdmissions(
    @Parent() record: OncologyConsultationHistoryModel,
  ): Promise<AdmissionModel[]> {
    return await this.manager
      .withRepository(this.admissionRepository)
      .createQueryBuilder('admissions')
      .innerJoin(
        'admissions.oncologyConsultations',
        'oncologyConsultations',
        'oncologyConsultations.id = :id',
        {
          id: record.id,
        },
      )
      .getMany();
  }

  @ResolveField('allergies', () => [AllergyModel])
  async getAllergies(
    @Parent() record: OncologyConsultationHistoryModel,
  ): Promise<AllergyModel[]> {
    return await this.manager
      .withRepository(this.allergyRepository)
      .createQueryBuilder('allergies')
      .innerJoin(
        'allergies.oncologyConsultations',
        'oncologyConsultations',
        'oncologyConsultations.id = :id',
        {
          id: record.id,
        },
      )
      .getMany();
  }

  @ResolveField('medications', () => [MedicationModel])
  async getMedications(
    @Parent() record: OncologyConsultationHistoryModel,
  ): Promise<MedicationModel[]> {
    return await this.manager
      .withRepository(this.medicationRepository)
      .createQueryBuilder('medications')
      .innerJoin(
        'medications.oncologyConsultations',
        'oncologyConsultations',
        'oncologyConsultations.id = :id',
        {
          id: record.id,
        },
      )
      .getMany();
  }

  @ResolveField('surgeries', () => [SurgeryModel])
  async getSurgeries(
    @Parent() record: OncologyConsultationHistoryModel,
  ): Promise<SurgeryModel[]> {
    return await this.manager
      .withRepository(this.surgeryRepository)
      .createQueryBuilder('surgeries')
      .innerJoin(
        'surgeries.oncologyConsultations',
        'oncologyConsultations',
        'oncologyConsultations.id = :id',
        {
          id: record.id,
        },
      )
      .getMany();
  }

  @ResolveField('vitals', () => [VitalModel])
  async getVitals(
    @Parent() record: OncologyConsultationHistoryModel,
  ): Promise<VitalModel[]> {
    return await this.manager
      .withRepository(this.vitalRepository)
      .createQueryBuilder('vitals')
      .innerJoin(
        'vitals.oncologyConsultations',
        'oncologyConsultations',
        'oncologyConsultations.id = :id',
        {
          id: record.id,
        },
      )
      .getMany();
  }

  @ResolveField('nursingServices', () => [NursingServiceModel])
  async getNursingServices(
    @Parent() root: OncologyConsultationHistoryModel,
  ): Promise<NursingServiceModel[]> {
    return this.manager
      .withRepository(this.nursingServiceRepository)
      .createQueryBuilder('nursingServices')
      .innerJoin(
        'nursingServices.oncologyConsultations',
        'oncologyConsultations',
        'oncologyConsultations.id = :id',
        {
          id: root.id,
        },
      )
      .getMany();
  }

  @ResolveField('radiology', () => [InvestigationModel])
  async getLinkedRadiologyInvestigations(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): Promise<InvestigationModel[]> {
    const { id } = root;

    return this.service.getLinkedRadiologyInvestigationRecords(profile, id);
  }

  @ResolveField('labTests', () => [InvestigationModel])
  async getLinkedLabInvestigations(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): Promise<InvestigationModel[]> {
    const { id } = root;

    return this.service.getLinkedLaboratoryInvestigationRecords(profile, id);
  }

  @ResolveField('investigations', () => [InvestigationModel])
  async getLinkedInvestigations(
    @CurrentProfile() profile: ProfileModel,
    @Parent() root: OncologyConsultationHistoryModel,
  ): Promise<InvestigationModel[]> {
    const { id } = root;
    return this.service.getLinkedInvestigationRecords(profile, id);
  }

  @Permissions(
    {
      action: PatientAction.ReadOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.ReadAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
  )
  @Query(() => [OncologyTreatmentPlanModel])
  async oncologyConsultationTreatmentPlans(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'consultationId', type: () => String, nullable: false })
    consultationId: string,
  ): Promise<OncologyTreatmentPlanModel[]> {
    return this.service.oncologyConsultationTreatmentPlans(
      profile,
      consultationId,
    );
  }

  @Permissions(
    {
      action: PatientAction.CreateOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.CreateTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
  )
  @UseGuards(PinAuthGuard)
  @Mutation(() => OncologyTreatmentPlanModel)
  async addOncologyConsultationTreatmentPlan(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => OncologyTreatmentPlanInput,
      nullable: false,
    })
    input: OncologyTreatmentPlanInput,
    @Args({ name: 'consultationId', type: () => String, nullable: false })
    consultationId: string,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    clinifyId: string,
    @Args({ name: 'id', nullable: true }) id?: string,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<OncologyTreatmentPlanModel> {
    const item = await this.service.addOncologyConsultationTreatmentPlan(
      profile,
      consultationId,
      { ...input, ...(id && { id }) },
    );
    await this.pubSub.publish(ConsultationEvent, {
      consultation: { profile: { clinifyId } },
      [ConsultationEvent]: EventType.UPDATED,
    });
    await this.pubSub.publish(OncologyConsultationTreatmentPlanAdded, {
      [OncologyConsultationTreatmentPlanAdded]: item,
    });
    return item;
  }

  @Subscription(() => OncologyTreatmentPlanModel, {
    name: OncologyConsultationTreatmentPlanAdded,
    filter: filterOncologyConsultationTreatmentPlanAdded,
  })
  addOncologyConsultationTreatmentPlanSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(OncologyConsultationTreatmentPlanAdded);
  }

  @Permissions(
    {
      action: PatientAction.UpdateOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
  )
  @UseGuards(PinAuthGuard)
  @Mutation(() => OncologyTreatmentPlanModel)
  async updateOncologyConsultationTreatmentPlan(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => OncologyTreatmentPlanInput,
      nullable: false,
    })
    input: OncologyTreatmentPlanInput,
    @Args({ name: 'id', type: () => String, nullable: false })
    id: string,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    clinifyId: string,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<OncologyTreatmentPlanModel> {
    const item = await this.service.updateOncologyConsultationTreatmentPlan(
      profile,
      id,
      input,
    );
    await this.pubSub.publish(ConsultationEvent, {
      consultation: { profile: { clinifyId } },
      [ConsultationEvent]: EventType.UPDATED,
    });
    await this.pubSub.publish(OncologyConsultationTreatmentPlanUpdated, {
      [OncologyConsultationTreatmentPlanUpdated]: item,
    });
    return item;
  }

  @Subscription(() => OncologyTreatmentPlanModel, {
    name: OncologyConsultationTreatmentPlanUpdated,
    filter: filterOncologyConsultationTreatmentPlanUpdated,
  })
  updateOncologyConsultationTreatmentPlanSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(OncologyConsultationTreatmentPlanUpdated);
  }

  @Permissions(
    {
      action: PatientAction.DeleteOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.DeleteTeamOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.DeleteTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
  )
  @Mutation(() => OncologyTreatmentPlanModel)
  async deleteOncologyConsultationTreatmentPlan(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'id', type: () => String, nullable: false })
    id: string,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    clinifyId: string,
  ): Promise<OncologyTreatmentPlanModel> {
    const item = await this.service.deleteOncologyConsultationTreatmentPlan(
      profile,
      id,
    );
    await this.pubSub.publish(ConsultationEvent, {
      consultation: { profile: { clinifyId } },
      [ConsultationEvent]: EventType.UPDATED,
    });
    await this.pubSub.publish(OncologyConsultationTreatmentPlanRemoved, {
      [OncologyConsultationTreatmentPlanRemoved]: item,
    });
    return item;
  }

  @Subscription(() => OncologyTreatmentPlanModel, {
    name: OncologyConsultationTreatmentPlanRemoved,
    filter: filterOncologyConsultationTreatmentPlanRemoved,
  })
  removeOncologyConsultationTreatmentPlanSubsHandler(
    @Args('profileId') _profileId: string,
  ): AsyncIterator<string> {
    return this.pubSub.asyncIterator(OncologyConsultationTreatmentPlanRemoved);
  }

  @Permissions(
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.ConsultationOncology,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.ConsultationOncology,
    },
  )
  @Mutation(() => OncologyTreatmentPlanModel)
  async concealOncologyConsultationTreatmentPlan(
    @CurrentProfile() profile: ProfileModel,
    @Args('treatmentPlanId') treatmentPlanId: string,
    @Args('concealStatus', { type: () => Boolean }) concealStatus: boolean,
  ): Promise<OncologyTreatmentPlanModel> {
    const item = await this.service.concealOncologyConsultationTreatmentPlan(
      profile,
      treatmentPlanId,
      concealStatus,
    );
    await this.pubSub.publish(OncologyConsultationTreatmentPlanUpdated, {
      [OncologyConsultationTreatmentPlanUpdated]: item,
    });
    return item;
  }

  @Permissions(
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
  )
  @Mutation(() => OncologyTreatmentPlanModel)
  async concealOncologyTreatmentPlanObservationNote(
    @CurrentProfile() profile: ProfileModel,
    @Args('treatmentPlanId') treatmentPlanId: string,
    @Args('concealStatus', { type: () => Boolean }) concealStatus: boolean,
  ): Promise<OncologyTreatmentPlanModel> {
    const item = await this.service.concealOncologyTreatmentPlanObservationNote(
      profile,
      treatmentPlanId,
      concealStatus,
    );
    await this.pubSub.publish(OncologyConsultationTreatmentPlanUpdated, {
      [OncologyConsultationTreatmentPlanUpdated]: item,
    });
    return item;
  }

  @Permissions(
    {
      action: PatientAction.UpdateOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
  )
  @UseGuards(PinAuthGuard)
  @Mutation(() => OncologyTreatmentPlanModel)
  async addOncologyTreatmentPlanConsentSignature(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => SignatureInput,
      nullable: false,
    })
    input: SignatureInput,
    @Args({ name: 'id', nullable: false }) id: string,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    clinifyId: string,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<OncologyTreatmentPlanModel> {
    const item = await this.service.saveOncologyTreatmentPlanConsentSignature(
      profile,
      id,
      input,
    );
    await this.pubSub.publish(OncologyConsultationTreatmentPlanUpdated, {
      [OncologyConsultationTreatmentPlanUpdated]: item,
    });
    return item;
  }

  @Permissions(
    {
      action: PatientAction.UpdateOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
  )
  @UseGuards(PinAuthGuard)
  @Mutation(() => OncologyTreatmentPlanModel)
  async updateOncologyTreatmentPlanConsentSignature(
    @CurrentProfile() profile: ProfileModel,
    @Args({
      name: 'input',
      type: () => SignatureInput,
      nullable: false,
    })
    input: SignatureInput,
    @Args({ name: 'id', nullable: false }) id: string,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    clinifyId: string,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<OncologyTreatmentPlanModel> {
    const item = await this.service.updateOncologyTreatmentPlanConsentSignature(
      profile,
      id,
      input,
    );
    await this.pubSub.publish(OncologyConsultationTreatmentPlanUpdated, {
      [OncologyConsultationTreatmentPlanUpdated]: item,
    });
    return item;
  }

  @Permissions(
    {
      action: PatientAction.UpdateOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
  )
  @Mutation(() => OncologyTreatmentPlanModel)
  async deleteOncologyTreatmentPlanConsentSignature(
    @CurrentProfile() profile: ProfileModel,
    @Args({ name: 'id', nullable: false }) id: string,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    clinifyId: string,
  ): Promise<OncologyTreatmentPlanModel> {
    const item = await this.service.removeOncologyTreatmentPlanConsentSignature(
      profile,
      id,
    );
    await this.pubSub.publish(OncologyConsultationTreatmentPlanUpdated, {
      [OncologyConsultationTreatmentPlanUpdated]: item,
    });
    return item;
  }

  @Permissions(
    {
      action: PatientAction.UpdateOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamOwn,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
    {
      action: OrganizationAction.UpdateTeamAny,
      subject: Subject.OncologyConsultationTreatmentPlan,
    },
  )
  @UseGuards(PinAuthGuard)
  @Mutation(() => OncologyChemoDrugModel)
  async updateChemoComment(
    @CurrentProfile() mutator: ProfileModel,
    @Args({ name: 'chemoDrugId', nullable: false }) chemoDrugId: string,
    @Args('input') input: OncologyChemoCommentInput,
    @Args({ name: 'clinifyId', type: () => String, nullable: false })
    clinifyId: string,
    @Args({ name: 'pin', type: () => String, nullable: true }) _pin?: string,
  ): Promise<OncologyChemoDrugModel> {
    const item = await this.service.updateChemoComment(
      mutator,
      chemoDrugId,
      input,
    );

    await this.pubSub.publish(MedicationEvent, {
      medication: { profile: { clinifyId } },
      [MedicationEvent]: EventType.UPDATED,
    });
    await this.pubSub.publish(ChemoDrugUpdated, {
      profileId: item?.oncologyConsultationHistory.profileId,
      [ChemoDrugUpdated]: item,
    });
    return item;
  }
}
