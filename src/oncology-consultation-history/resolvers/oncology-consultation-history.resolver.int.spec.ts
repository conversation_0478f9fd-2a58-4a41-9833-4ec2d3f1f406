/* eslint-disable max-lines */
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { EntityManager, DataSource } from 'typeorm';
import { OncologyConsultationHistoryResolver } from './oncology-consultation-history.resolver';
import { NotificationsService } from '../../notifications/services/notifications.service';
import { OncologyConsultationHistoryService } from '../services/oncology-consultation-history.service';
import { OncologyConsultationLinkedRecordType } from '../validators/oncology-consultation-history.input';
import { appointmentFactory } from '@clinify/__mocks__/factories/appointment.factory';
import {
  billDetailsFactory,
  billFactory,
} from '@clinify/__mocks__/factories/bill.factory';
import { treatmentPlanFactory } from '@clinify/__mocks__/factories/consultation.factory';
import { hospitalFactory } from '@clinify/__mocks__/factories/hospital.factory';
import {
  oncologyChemoDrugFactory,
  oncologyConsultationFactory,
} from '@clinify/__mocks__/factories/oncologyConsultation.factory';
import { preauthorizationDetailsFactory } from '@clinify/__mocks__/factories/preauthorizationdetails.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';
import { AdmissionModel } from '@clinify/admissions/models/admission.model';
import { AllergyModel } from '@clinify/allergies/models/allergy.model';
import { OrganisationAppointmentModel } from '@clinify/appointments/models/organisation_appointment.model';
import { PermissionModel } from '@clinify/authorization/models/permission.model';
import { PermissionService } from '@clinify/authorization/services/permission.service';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { NursingServiceModel } from '@clinify/nursing-services/models/nursing-services.model';
import { PreauthorizationDetailsModel } from '@clinify/preauthorization-details/models/preauthorization-details.model';
import { PreauthorizationDetailsService } from '@clinify/preauthorization-details/services/preauthorization-details.service';
import { UserType } from '@clinify/shared/enums/users';
import { SurgeryModel } from '@clinify/surgeries/models/surgery.model';
import { OncologyChartType } from '@clinify/users/inputs/oncology-register.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { VitalModel } from '@clinify/vitals/models/vital.model';
import { managerMock } from '@mocks/database.mock';
import { hmoClaimFactory } from '@mocks/factories/hmo-claim.factory';
import { mockUser } from '@mocks/factories/user.factory';

const profile = profileFactory.build();
const user = mockUser;
const oncologyData = oncologyConsultationFactory.build();
const oncologyRegister = oncologyData.oncologyRegister;
const hmoClaimData = hmoClaimFactory.build();
const appointmentData = appointmentFactory.build();
const treatmentPlanData = treatmentPlanFactory.build();

const oncologyConsultationServiceMock = {
  saveOncologyHistory: jest.fn(() => oncologyData),
  getOncologyHistory: jest.fn(() => oncologyData),
  updateOncologyHistory: jest.fn(() => oncologyData),
  deleteOncologyHistory: jest.fn(() => [oncologyData]),
  archiveOncologyHistory: jest.fn(() => [oncologyData]),
  updateOncologyRegister: jest.fn(() => oncologyRegister),
  concealConsultationComplaint: jest.fn(),
  concealConsultationComplaintHistory: jest.fn(),
  concealConsultationSystemReview: jest.fn(),
  concealConsultationPhysicalExam: jest.fn(),
  concealConsultationAudiometry: jest.fn(),
  concealConsultationHealthEducation: jest.fn(),
  concealOncologyConsultationChemoNote: jest.fn(),
  addChemoDrug: jest.fn(),
  updateChemoDrug: jest.fn(),
  deleteChemoDrug: jest.fn(),
  administerChemoDrug: jest.fn(),
  linkRecordsToOncologyConsultation: jest.fn(),
  getLinkedRadiologyInvestigationRecords: jest.fn(),
  getLinkedLaboratoryInvestigationRecords: jest.fn(),
  getLinkedInvestigationRecords: jest.fn(),
  isMedicationsLinked: jest.fn(),
  isAllergiesLinked: jest.fn(),
  isSurgeriesLinked: jest.fn(),
  isVitalsLinked: jest.fn(),
  isLaboratoryInvestigationLinked: jest.fn(),
  isRadiologyInvestigationLinked: jest.fn(),
  addOncologyConsultationTreatmentPlan: jest.fn(() => treatmentPlanData),
  updateOncologyConsultationTreatmentPlan: jest.fn(() => treatmentPlanData),
  deleteOncologyConsultationTreatmentPlan: jest.fn(() => treatmentPlanData),
  oncologyConsultationTreatmentPlans: jest.fn(() => treatmentPlanData),
  concealOncologyConsultationTreatmentPlan: jest.fn(() => treatmentPlanData),
  concealOncologyTreatmentPlanObservationNote: jest.fn(() => treatmentPlanData),
  saveOncologyTreatmentPlanConsentSignature: jest.fn(() => treatmentPlanData),
  updateOncologyTreatmentPlanConsentSignature: jest.fn(() => treatmentPlanData),
  removeOncologyTreatmentPlanConsentSignature: jest.fn(() => treatmentPlanData),
  updateChemoComment: jest.fn(),
};

const MockProfileRepository = {
  findOne: jest.fn(() => user.defaultProfile),
};

const ManagerMock = {
  findOneOrFail: jest.fn(() => user.defaultProfile),
  ...managerMock,
};

const pubSubMock = {
  publish: jest.fn(),
  asyncIterator: jest.fn(),
};

const mockEventEmitter = {
  emit: jest.fn(),
};

const preauthorizationDetailsRepoMock = {
  find: jest.fn(() => [preauthorizationDetailsFactory.build()]),
};

const mockHmoClaimService = {
  getHmoClaim: jest.fn(() => hmoClaimData),
};

const mockAppointmentRepository = {
  findOne: jest.fn(() => appointmentData),
};

const handleNoticationEventFunction = jest.fn();

describe('OncologyConsultationHistoryResolver', () => {
  let resolver: OncologyConsultationHistoryResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OncologyConsultationHistoryResolver,
        OncologyConsultationHistoryService,
        PermissionService,
        PreauthorizationDetailsService,
        {
          provide: OncologyConsultationHistoryService,
          useValue: oncologyConsultationServiceMock,
        },
        {
          provide: getRepositoryToken(PermissionModel),
          useValue: MockProfileRepository,
        },
        {
          provide: getRepositoryToken(ProfileModel),
          useValue: MockProfileRepository,
        },
        {
          provide: getRepositoryToken(PreauthorizationDetailsModel),
          useValue: preauthorizationDetailsRepoMock,
        },
        {
          provide: getRepositoryToken(OrganisationAppointmentModel),
          useValue: mockAppointmentRepository,
        },
        {
          provide: EntityManager,
          useValue: ManagerMock,
        },
        {
          provide: DataSource,
          useValue: { manager: ManagerMock },
        },
        {
          provide: getRepositoryToken(VitalModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(AllergyModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(MedicationModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(SurgeryModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(NursingServiceModel),
          useValue: {},
        },
        {
          provide: getRepositoryToken(AdmissionModel),
          useValue: {},
        },
        {
          provide: 'PUB_SUB',
          useValue: pubSubMock,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: NotificationsService,
          useValue: {
            handleNoticationEvent: handleNoticationEventFunction,
          },
        },
        {
          provide: HmoClaimService,
          useValue: mockHmoClaimService,
        },
      ],
    }).compile();

    resolver = module.get<OncologyConsultationHistoryResolver>(
      OncologyConsultationHistoryResolver,
    );
    jest.clearAllMocks();
  });

  it('addOncologyConsultationHistory(): should save oncology consultation', async () => {
    const oncologyConsultationInput = oncologyData;
    delete oncologyConsultationInput.profile;
    const response = await resolver.addOncologyConsultationHistory(
      profile,
      oncologyConsultationInput,
    );
    expect(
      oncologyConsultationServiceMock.saveOncologyHistory,
    ).toHaveBeenCalledWith(profile, oncologyConsultationInput, undefined);
    expect(response).toEqual(oncologyData);

    expect(pubSubMock.publish).toHaveBeenCalledTimes(2);
    expect(handleNoticationEventFunction).toHaveBeenCalledTimes(1);
  });
  it('addOncologyConsultationHistory(): should save oncology consultation and prescribe', async () => {
    const oncologyConsultationInput = oncologyData;
    delete oncologyConsultationInput.profile;
    const response = await resolver.addOncologyConsultationHistory(
      profile,
      oncologyConsultationInput,
      undefined,
      true,
    );
    expect(
      oncologyConsultationServiceMock.saveOncologyHistory,
    ).toHaveBeenCalledWith(profile, oncologyConsultationInput, true);
    expect(response).toEqual(oncologyData);

    expect(pubSubMock.publish).toHaveBeenCalledTimes(2);
    expect(handleNoticationEventFunction).toHaveBeenCalledTimes(1);
  });

  it('addOncologyConsultationHistory(): should save oncology consultation with bill', async () => {
    const oncologyConsultationInput = oncologyData;
    delete oncologyConsultationInput.profile;

    const responseWithBill = {
      ...oncologyData,
      bill: billFactory.build(),
    };

    oncologyConsultationServiceMock.saveOncologyHistory = jest.fn(
      () => responseWithBill,
    );
    const response = await resolver.addOncologyConsultationHistory(
      profile,
      oncologyConsultationInput,
    );
    expect(
      oncologyConsultationServiceMock.saveOncologyHistory,
    ).toHaveBeenCalledWith(profile, oncologyConsultationInput, undefined);
    expect(response).toEqual(responseWithBill);

    expect(pubSubMock.publish).toHaveBeenCalledTimes(4);
    expect(handleNoticationEventFunction).toHaveBeenCalledTimes(2);
  });

  it('addOncologySubsHandler(): should trigger OncologyConsultationAdded subscription', () => {
    resolver.addOncologySubsHandler('profile-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'OncologyConsultationAdded',
    );
  });

  it('oncologyConsultationHistory(): should get one oncology consultation', async () => {
    const oncologyConsultationInput = oncologyData;
    delete oncologyConsultationInput.profile;
    const clinifyId = 'clinify-id';
    const response = await resolver.oncologyConsultationHistory(
      profile,
      oncologyConsultationInput.id,
      clinifyId,
    );
    expect(
      oncologyConsultationServiceMock.getOncologyHistory,
    ).toHaveBeenCalledWith(profile, oncologyConsultationInput.id);
    expect(response).toEqual(oncologyData);
  });

  it('updateOncologyConsultationHistory(): should update oncology consultation', async () => {
    const oncologyConsultationInput = oncologyData;
    delete oncologyConsultationInput.profile;
    const response = await resolver.updateOncologyConsultationHistory(
      profile,
      oncologyConsultationInput.id,
      oncologyConsultationInput,
    );
    expect(
      oncologyConsultationServiceMock.updateOncologyHistory,
    ).toHaveBeenCalledWith(
      profile,
      oncologyConsultationInput.id,
      oncologyConsultationInput,
      false,
      undefined,
    );
    expect(response).toEqual(oncologyData);
  });

  it('updateOncologyConsultationHistoryBill(): should update oncology consultation with bill data', async () => {
    const oncologyConsultationInput = {
      ...oncologyData,
      clinifyId: 'clinify-id',
      serviceDetails: [
        {
          type: 'Service Type',
          name: 'Service Name',
          quantity: '2',
          pricePerUnit: '3000',
        },
      ],
    };
    delete oncologyConsultationInput.profile;
    const response = await resolver.updateOncologyConsultationHistoryBill(
      profile,
      oncologyConsultationInput.id,
      oncologyConsultationInput,
    );
    expect(
      oncologyConsultationServiceMock.updateOncologyHistory,
    ).toHaveBeenCalledWith(
      profile,
      oncologyConsultationInput.id,
      {
        id: oncologyConsultationInput.id,
        clinifyId: 'clinify-id',
        serviceDetails: oncologyConsultationInput.serviceDetails,
      },
      true,
    );
    expect(response).toEqual(oncologyData);
  });

  it('updateOncologySubsHandler(): should trigger OncologyConsultationUpdated subscription', () => {
    resolver.updateOncologySubsHandler('profile-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'OncologyConsultationUpdated',
    );
  });

  it('deleteOncologyConsultationHistory(): should delete oncology consultation', async () => {
    const oncologyConsultationInput = oncologyData;
    const clinifyId = user.defaultProfile.clinifyId;
    delete oncologyConsultationInput.profile;
    const response = await resolver.deleteOncologyConsultationHistory(
      profile,
      [oncologyConsultationInput.id],
      clinifyId,
    );
    expect(
      oncologyConsultationServiceMock.deleteOncologyHistory,
    ).toHaveBeenCalledWith(profile, [oncologyConsultationInput.id]);
    expect(response).toEqual([oncologyData]);
  });

  it('removeOncologySubsHandler(): should trigger OncologyConsultationRemoved subscription', () => {
    resolver.removeOncologySubsHandler('profile-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'OncologyConsultationRemoved',
    );
  });

  it('updateOncologyConsultationRegister(): should update oncology consultation', async () => {
    const oncologyRegisterInput = oncologyRegister;
    delete oncologyRegisterInput.profile;
    const response = await resolver.updateOncologyConsultationRegister(
      profile,
      oncologyRegisterInput.id,
      OncologyChartType.Treatment,
      oncologyRegisterInput,
      'clinify-id',
    );
    expect(
      oncologyConsultationServiceMock.updateOncologyRegister,
    ).toHaveBeenCalledWith(
      profile,
      oncologyRegisterInput.id,
      OncologyChartType.Treatment,
      oncologyRegisterInput,
    );
    expect(response).toEqual(oncologyRegister);

    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'OncologyConsultationChartUpdated',
      {
        OncologyConsultationChartUpdated: oncologyRegister,
      },
    );
  });

  it('updateOncologyChartSubsHandler(): should trigger OncologyConsultationChartUpdated subscription', () => {
    resolver.updateOncologyChartSubsHandler('profile-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'OncologyConsultationChartUpdated',
    );
  });

  it('archiveOncologyConsultationHistory(): should archive oncology consultation', async () => {
    const oncologyConsultationInput = oncologyData;
    const clinifyId = user.defaultProfile.clinifyId;
    delete oncologyConsultationInput.profile;
    const response = await resolver.archiveOncologyConsultationHistory(
      profile,
      [oncologyConsultationInput.id],
      true,
      clinifyId,
    );
    expect(
      oncologyConsultationServiceMock.archiveOncologyHistory,
    ).toHaveBeenCalledWith(profile, [oncologyConsultationInput.id], true);
    expect(response).toEqual([oncologyData]);

    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'OncologyConsultationArchived',
      {
        OncologyConsultationArchived: response,
      },
    );
  });

  it('archiveOncologyConsultationHistory(): can publish OncologyConsultationUnarchived subscription', async () => {
    const oncologyConsultationInput = oncologyData;
    const clinifyId = user.defaultProfile.clinifyId;
    delete oncologyConsultationInput.profile;
    const response = await resolver.archiveOncologyConsultationHistory(
      profile,
      [oncologyConsultationInput.id],
      false,
      clinifyId,
    );
    expect(
      oncologyConsultationServiceMock.archiveOncologyHistory,
    ).toHaveBeenCalledWith(profile, [oncologyConsultationInput.id], false);
    expect(response).toEqual([oncologyData]);

    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'OncologyConsultationUnarchived',
      {
        OncologyConsultationUnarchived: response,
      },
    );
  });

  it('archiveOncologySubsHandler(): should trigger OncologyConsultationArchived subscription', () => {
    resolver.archiveOncologySubsHandler('profile-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'OncologyConsultationArchived',
    );
  });

  it('unarchiveOncologySubsHandler(): should trigger OncologyConsultationUnarchived subscription', () => {
    resolver.unarchiveOncologySubsHandler('profile-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'OncologyConsultationUnarchived',
    );
  });

  it('getBill(): should get oncology consultation bills', () => {
    const newHospitals = hospitalFactory.buildList(2);
    const newBill = billDetailsFactory.build();
    const oncologyConsultationBills = resolver.getBill(
      {
        ...profile,
        hospital: newHospitals[0],
      },
      {
        ...oncologyData,
        profile: profileFactory.build(),
        hospital: newHospitals[0],
        createdBy: profile,
        hospitalId: profile.hospitalId,
        bill: newBill,
      },
    );

    expect(oncologyConsultationBills).toStrictEqual(newBill);
  });

  it('getBill(): should get oncology consultation bills', () => {
    const newHospitals = hospitalFactory.buildList(2);
    const newBill = billDetailsFactory.build();
    const oncologyConsultationBills = resolver.getBill(
      {
        ...profile,
        defaultProfile: {
          ...profile,
          hospital: newHospitals[0],
        },
      },
      {
        ...oncologyData,
        createdBy: {
          ...oncologyData.createdBy,
          hospitalId: newHospitals[1].id,
        },
        profile: profileFactory.build(),
        hospital: newHospitals[1],
        hospitalId: profile.hospitalId,
        bill: newBill,
      },
    );

    expect(oncologyConsultationBills).toStrictEqual(null);
  });

  it('getPreauthorizationDetails(): should get preauthorization details', async () => {
    const oncologyConsultationDataToUse = {
      ...oncologyData,
      hospitalId: profile.hospitalId,
    };
    preauthorizationDetailsRepoMock.find.mockReturnValue([
      oncologyConsultationDataToUse,
    ]);
    const res2 = await resolver.getPreauthorizationDetails(
      profile,
      oncologyConsultationDataToUse,
    );

    expect(res2.id).toEqual(oncologyConsultationDataToUse.id);
  });

  it('getHmoClaim(): should call getRecordHmoClaim service', async () => {
    const response = await resolver.getHmoClaim(profile, {
      ...oncologyData,
      hospitalId: profile.hospitalId,
      hmoClaimId: 'id',
    });
    expect(profile.hospitalId).toBeTruthy();
    expect(profile.hospitalId.length).toBeGreaterThan(10);
    expect(mockHmoClaimService.getHmoClaim).toHaveBeenCalledWith(profile, 'id');
    expect(response).toEqual(hmoClaimData);
  });

  it('getHmoClaim(): should not call getRecordHmoClaim service', async () => {
    const response = await resolver.getHmoClaim(profile, oncologyData);
    expect(profile.hospitalId).toBeTruthy();
    expect(profile.hospitalId.length).toBeGreaterThan(10);
    expect(mockHmoClaimService.getHmoClaim).not.toHaveBeenCalled();
    expect(response).toEqual(null);
  });

  it('getAppointment(): should get appointment associated with this record', async () => {
    const response = await resolver.getAppointment({
      ...oncologyData,
      appointmentId: null,
    });

    expect(mockAppointmentRepository.findOne).not.toHaveBeenCalled();
    expect(response).toEqual(null);
  });

  it('getAppointment(): should get appointment associated with this record', async () => {
    const response = await resolver.getAppointment({
      ...oncologyData,
      appointmentId: 'appointment-id',
    });

    expect(mockAppointmentRepository.findOne).toHaveBeenCalled();
    expect(response).toEqual(appointmentData);
  });

  it('getComplaint(): should toggle conceal state and return complaint', () => {
    const patient = profileFactory.build();
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
    });
    const input = oncologyConsultationFactory.build({
      profile: patient,
      createdBy: mutator,
    });

    const response1 = resolver.getComplaint(patient, {
      ...input,
      complaint: 'Note',
      concealComplaint: false,
    });
    expect(response1).toEqual('Note');

    const response2 = resolver.getComplaint(patient, {
      ...input,
      complaint: 'Note',
      concealComplaint: true,
    });
    expect(response2).toEqual(null);
  });

  it('getComplaintHistory(): should toggle conceal state and return complaintHistory', () => {
    const patient = profileFactory.build();
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
    });
    const input = oncologyConsultationFactory.build({
      profile: patient,
      createdBy: mutator,
    });

    const response1 = resolver.getComplaintHistory(patient, {
      ...input,
      complaintHistory: 'Note',
      concealComplaintHistory: false,
    });
    expect(response1).toEqual('Note');

    const response2 = resolver.getComplaintHistory(patient, {
      ...input,
      complaintHistory: 'Note',
      concealComplaintHistory: true,
    });
    expect(response2).toEqual(null);
  });

  it('getSystemReview(): should toggle conceal state and return systemReview', () => {
    const patient = profileFactory.build();
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
    });
    const input = oncologyConsultationFactory.build({
      profile: patient,
      createdBy: mutator,
    });

    const response1 = resolver.getSystemReview(patient, {
      ...input,
      systemReview: 'Note',
      concealSystemReview: false,
    });
    expect(response1).toEqual('Note');

    const response2 = resolver.getSystemReview(patient, {
      ...input,
      systemReview: 'Note',
      concealSystemReview: true,
    });
    expect(response2).toEqual(null);
  });

  it('getPhysicalExam(): should toggle conceal state and return physicalExam', () => {
    const patient = profileFactory.build();
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
    });
    const input = oncologyConsultationFactory.build({
      profile: patient,
      createdBy: mutator,
    });

    const response1 = resolver.getPhysicalExam(patient, {
      ...input,
      physicalExam: 'Note',
      concealPhysicalExam: false,
    });
    expect(response1).toEqual('Note');

    const response2 = resolver.getPhysicalExam(patient, {
      ...input,
      physicalExam: 'Note',
      concealPhysicalExam: true,
    });
    expect(response2).toEqual(null);
  });

  it('getAudiometry(): should toggle conceal state and return audiometry', () => {
    const patient = profileFactory.build();
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
    });
    const input = oncologyConsultationFactory.build({
      profile: patient,
      createdBy: mutator,
    });

    const response1 = resolver.getAudiometry(patient, {
      ...input,
      audiometry: 'Note',
      concealAudiometry: false,
    });
    expect(response1).toEqual('Note');

    const response2 = resolver.getAudiometry(patient, {
      ...input,
      audiometry: 'Note',
      concealAudiometry: true,
    });
    expect(response2).toEqual(null);
  });

  it('getHealthEducation(): should toggle conceal state and return healthEducation', () => {
    const patient = profileFactory.build();
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
    });
    const input = oncologyConsultationFactory.build({
      profile: patient,
      createdBy: mutator,
    });

    const response1 = resolver.getHealthEducation(patient, {
      ...input,
      healthEducation: 'Note',
      concealHealthEducation: false,
    });
    expect(response1).toEqual('Note');

    const response2 = resolver.getHealthEducation(patient, {
      ...input,
      healthEducation: 'Note',
      concealHealthEducation: true,
    });
    expect(response2).toEqual(null);
  });

  it('getChemoNote(): should toggle conceal state and return chemoNote', () => {
    const patient = profileFactory.build();
    const mutator = profileFactory.build({
      type: UserType.OrganizationRecordOfficer,
    });
    const input = oncologyConsultationFactory.build({
      profile: patient,
      createdBy: mutator,
    });

    const response1 = resolver.getChemoNote(patient, {
      ...input,
      chemoNote: 'Note',
      concealChemoNote: false,
    });
    expect(response1).toEqual('Note');

    const response2 = resolver.getChemoNote(patient, {
      ...input,
      chemoNote: 'Note',
      concealChemoNote: true,
    });
    expect(response2).toEqual(null);
  });

  it('concealOncologyConsultationComplaint(): should call concealConsultationComplaint service method', async () => {
    const mutator = profileFactory.build();

    await resolver.concealOncologyConsultationComplaint(
      mutator,
      'oncology-id',
      false,
    );

    expect(
      oncologyConsultationServiceMock.concealConsultationComplaint,
    ).toHaveBeenCalledWith(mutator, 'oncology-id', false);
  });

  it('concealOncologyConsultationComplaintHistory(): should call concealConsultationComplaintHistory service method', async () => {
    const mutator = profileFactory.build();

    await resolver.concealOncologyConsultationComplaintHistory(
      mutator,
      'oncology-id',
      false,
    );

    expect(
      oncologyConsultationServiceMock.concealConsultationComplaintHistory,
    ).toHaveBeenCalledWith(mutator, 'oncology-id', false);
  });

  it('concealOncologyConsultationSystemReview(): should call concealConsultationSystemReview service method', async () => {
    const mutator = profileFactory.build();

    await resolver.concealOncologyConsultationSystemReview(
      mutator,
      'oncology-id',
      false,
    );

    expect(
      oncologyConsultationServiceMock.concealConsultationSystemReview,
    ).toHaveBeenCalledWith(mutator, 'oncology-id', false);
  });

  it('concealOncologyConsultationPhysicalExam(): should call concealConsultationPhysicalExam service method', async () => {
    const mutator = profileFactory.build();

    await resolver.concealOncologyConsultationPhysicalExam(
      mutator,
      'oncology-id',
      false,
    );

    expect(
      oncologyConsultationServiceMock.concealConsultationPhysicalExam,
    ).toHaveBeenCalledWith(mutator, 'oncology-id', false);
  });

  it('concealOncologyConsultationAudiometry(): should call concealConsultationAudiometry service method', async () => {
    const mutator = profileFactory.build();

    await resolver.concealOncologyConsultationAudiometry(
      mutator,
      'oncology-id',
      false,
    );

    expect(
      oncologyConsultationServiceMock.concealConsultationAudiometry,
    ).toHaveBeenCalledWith(mutator, 'oncology-id', false);
  });

  it('concealOncologyConsultationHealthEducation(): should call concealConsultationHealthEducation service method', async () => {
    const mutator = profileFactory.build();

    await resolver.concealOncologyConsultationHealthEducation(
      mutator,
      'oncology-id',
      false,
    );

    expect(
      oncologyConsultationServiceMock.concealConsultationHealthEducation,
    ).toHaveBeenCalledWith(mutator, 'oncology-id', false);
  });

  it('concealOncologyConsultationChemoNote(): should call concealOncologyConsultationChemoNote service method', async () => {
    const mutator = profileFactory.build();

    await resolver.concealOncologyConsultationChemoNote(
      mutator,
      'oncology-id',
      false,
    );

    expect(
      oncologyConsultationServiceMock.concealOncologyConsultationChemoNote,
    ).toHaveBeenCalledWith(mutator, 'oncology-id', false);
  });

  it('addChemoDrug(): should call addChemoDrug service method', async () => {
    const mutator = profileFactory.build();
    const input = {
      ...oncologyData,
      chemoDrug: {
        name: 'drug',
        cycleNumber: 1,
      },
    };

    await resolver.addChemoDrug(mutator, input);

    expect(oncologyConsultationServiceMock.addChemoDrug).toHaveBeenCalledWith(
      mutator,
      input,
    );
  });

  it('updateChemoDrug(): should call updateChemoDrug service method', async () => {
    const mutator = profileFactory.build();
    const input = {
      ...oncologyData,
      chemoDrug: {
        name: 'drug',
        cycleNumber: 1,
      },
    };

    await resolver.updateChemoDrug(mutator, input);

    expect(
      oncologyConsultationServiceMock.updateChemoDrug,
    ).toHaveBeenCalledWith(mutator, input);
  });

  it('deleteChemoDrug(): should call deleteChemoDrug service method', async () => {
    await resolver.deleteChemoDrug('id');

    expect(
      oncologyConsultationServiceMock.deleteChemoDrug,
    ).toHaveBeenCalledWith('id');
  });

  it('administerChemoDrug(): should call administerChemoDrug service method and publish subscription', async () => {
    const mutator = profileFactory.build();
    const chemoDrug = oncologyChemoDrugFactory.build({
      medicationDetailsId: null,
    });

    delete chemoDrug.oncologyConsultationHistory;

    oncologyConsultationServiceMock.administerChemoDrug = jest
      .fn()
      .mockResolvedValue({
        ...chemoDrug,
        oncologyConsultationHistory: { profileId: 'profile-id' },
      });

    await resolver.administerChemoDrug(
      mutator,
      'id',
      'period',
      true,
      'clinify-id',
    );

    expect(
      oncologyConsultationServiceMock.administerChemoDrug,
    ).toHaveBeenCalledWith(mutator, 'id', 'period', true);
    expect(pubSubMock.publish).toHaveBeenCalledTimes(2);
    expect(pubSubMock.publish).toHaveBeenCalledWith('ConsultationEvent', {
      consultation: chemoDrug,
      ConsultationEvent: 'Updated',
    });
    expect(pubSubMock.publish).toHaveBeenCalledWith('ChemoDrugUpdated', {
      profileId: 'profile-id',
      ChemoDrugUpdated: chemoDrug,
    });
  });

  it('administerChemoDrug(): should call administerChemoDrug service method and publish subscription with prescription', async () => {
    const mutator = profileFactory.build();
    const chemoDrug = oncologyChemoDrugFactory.build({
      medicationDetailsId: 'medication-details-id',
    });

    delete chemoDrug.oncologyConsultationHistory;

    oncologyConsultationServiceMock.administerChemoDrug = jest
      .fn()
      .mockResolvedValue({
        ...chemoDrug,
        oncologyConsultationHistory: { profileId: 'profile-id' },
      });

    await resolver.administerChemoDrug(
      mutator,
      'id',
      'period',
      true,
      'clinify-id',
    );

    expect(
      oncologyConsultationServiceMock.administerChemoDrug,
    ).toHaveBeenCalledWith(mutator, 'id', 'period', true);
    expect(pubSubMock.publish).toHaveBeenCalledTimes(3);
    expect(pubSubMock.publish).toHaveBeenCalledWith('ConsultationEvent', {
      consultation: chemoDrug,
      ConsultationEvent: 'Updated',
    });
    expect(pubSubMock.publish).toHaveBeenCalledWith('ChemoDrugUpdated', {
      profileId: 'profile-id',
      ChemoDrugUpdated: chemoDrug,
    });
    expect(pubSubMock.publish).toHaveBeenCalledWith('MedicationEvent', {
      medication: { profile: { clinifyId: 'clinify-id' } },
      MedicationEvent: 'Updated',
    });
  });

  it('updateChemoDrugSubsHandler(): should call pubsub asyncIterator method with ChemoDrugUpdated', async () => {
    await resolver.updateChemoDrugSubsHandler('profile-id');

    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith('ChemoDrugUpdated');
  });

  it('linkRecordsToOncologyConsultation(): should call linkRecordsToOncologyConsultation service method', async () => {
    const mutator = profileFactory.build();
    await resolver.linkRecordsToOncologyConsultation(
      mutator,
      'oncology-id',
      OncologyConsultationLinkedRecordType.Allergy,
      ['record-id'],
    );

    expect(
      oncologyConsultationServiceMock.linkRecordsToOncologyConsultation,
    ).toHaveBeenCalledWith(
      mutator,
      'oncology-id',
      OncologyConsultationLinkedRecordType.Allergy,
      ['record-id'],
    );
  });

  it('getAdmissions(): should get oncology consultations admission record', async () => {
    await resolver.getAdmissions(oncologyData);
    expect(ManagerMock.withRepository).toHaveBeenCalledTimes(1);
  });

  it('getAllergies(): should get oncology consultations allergy record', async () => {
    await resolver.getAllergies(oncologyData);
    expect(ManagerMock.withRepository).toHaveBeenCalledTimes(1);
  });

  it('getMedications(): should get oncology consultations medication record', async () => {
    await resolver.getMedications(oncologyData);
    expect(ManagerMock.withRepository).toHaveBeenCalledTimes(1);
  });

  it('getSurgeries(): should get oncology consultations surgery record', async () => {
    await resolver.getSurgeries(oncologyData);
    expect(ManagerMock.withRepository).toHaveBeenCalledTimes(1);
  });

  it('getVitals(): should get oncology consultations surgery record', async () => {
    await resolver.getVitals(oncologyData);
    expect(ManagerMock.withRepository).toHaveBeenCalledTimes(1);
  });

  it('getNursingServices(): should get oncology consultations surgery record', async () => {
    await resolver.getNursingServices(oncologyData);
    expect(ManagerMock.withRepository).toHaveBeenCalledTimes(1);
  });

  it('getLinkedRadiologyInvestigation(): should call getLinkedRadiologyInvestigationRecords() service method', async () => {
    await resolver.getLinkedRadiologyInvestigations(profile, oncologyData);
    expect(
      oncologyConsultationServiceMock.getLinkedRadiologyInvestigationRecords,
    ).toHaveBeenCalledWith(profile, oncologyData.id);
  });

  it('getLinkedLabInvestigations(): should call getLinkedLaboratoryInvestigationRecords() service method', async () => {
    await resolver.getLinkedLabInvestigations(profile, oncologyData);
    expect(
      oncologyConsultationServiceMock.getLinkedLaboratoryInvestigationRecords,
    ).toHaveBeenCalledWith(profile, oncologyData.id);
  });

  it('getLinkedInvestigations(): should call getLinkedLaboratoryInvestigationRecords() service method', async () => {
    await resolver.getLinkedInvestigations(profile, oncologyData);
    expect(
      oncologyConsultationServiceMock.getLinkedInvestigationRecords,
    ).toHaveBeenCalledWith(profile, oncologyData.id);
  });

  it('oncologyConsultationTreatmentPlans(): shoulc call oncologyConsultationTreatmentPlans service method', async () => {
    const oncologyConsultationId = oncologyData.id;

    const response = await resolver.oncologyConsultationTreatmentPlans(
      profile,
      oncologyConsultationId,
    );

    expect(
      oncologyConsultationServiceMock.oncologyConsultationTreatmentPlans,
    ).toHaveBeenCalledWith(profile, oncologyConsultationId);
    expect(response).toEqual(treatmentPlanData);
  });

  it('addOncologyConsultationTreatmentPlan(): should call addOncologyConsultationTreatmentPlan service method', async () => {
    const oncologyConsultationId = oncologyData.id;

    const response = await resolver.addOncologyConsultationTreatmentPlan(
      profile,
      treatmentPlanData,
      oncologyConsultationId,
      'clinify-id',
    );

    expect(
      oncologyConsultationServiceMock.addOncologyConsultationTreatmentPlan,
    ).toHaveBeenCalledWith(profile, oncologyConsultationId, treatmentPlanData);
    expect(response).toEqual(treatmentPlanData);
  });

  it('addOncologyConsultationTreatmentPlanSubsHandler(): should trigger OncologyConsultationTreatmentPlanAdded subscription', () => {
    resolver.addOncologyConsultationTreatmentPlanSubsHandler('profile-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'OncologyConsultationTreatmentPlanAdded',
    );
  });

  it('updateOncologyConsultationTreatmentPlan(): should call updateOncologyConsultationTreatmentPlan service method', async () => {
    const response = await resolver.updateOncologyConsultationTreatmentPlan(
      profile,
      treatmentPlanData,
      treatmentPlanData.id,
      'clinify-id',
    );
    expect(
      oncologyConsultationServiceMock.updateOncologyConsultationTreatmentPlan,
    ).toHaveBeenCalledWith(profile, treatmentPlanData.id, treatmentPlanData);
    expect(response).toEqual(treatmentPlanData);
  });

  it('updateOncologyConsultationTreatmentPlanSubsHandler(): should trigger OncologyConsultationTreatmentPlanUpdated subscription', () => {
    resolver.updateOncologyConsultationTreatmentPlanSubsHandler('profile-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'OncologyConsultationTreatmentPlanUpdated',
    );
  });

  it('deleteOncologyConsultationTreatmentPlan(): should call deleteOncologyConsultationTreatmentPlan service method', async () => {
    const { id } = treatmentPlanData;
    const response = await resolver.deleteOncologyConsultationTreatmentPlan(
      profile,
      id,
      'clinify-id',
    );
    expect(
      oncologyConsultationServiceMock.deleteOncologyConsultationTreatmentPlan,
    ).toHaveBeenCalledWith(profile, id);
    expect(response).toEqual(treatmentPlanData);
  });

  it('removeOncologyConsultationTreatmentPlanSubsHandler(): should trigger OncologyConsultationTreatmentPlanRemoved subscription', () => {
    resolver.removeOncologyConsultationTreatmentPlanSubsHandler('profile-id');
    expect(pubSubMock.asyncIterator).toHaveBeenCalledWith(
      'OncologyConsultationTreatmentPlanRemoved',
    );
  });

  it('concealOncologyConsultationTreatmentPlan(): should call concealOncologyConsultationTreatmentPlan service method', async () => {
    await resolver.concealOncologyConsultationTreatmentPlan(
      profile,
      'treatment-plan-id',
      true,
    );

    expect(
      oncologyConsultationServiceMock.concealOncologyConsultationTreatmentPlan,
    ).toHaveBeenCalledWith(profile, 'treatment-plan-id', true);
  });

  it('concealOncologyConsultationTreatmentPlan(): should call concealOncologyConsultationTreatmentPlan service nethod', async () => {
    await resolver.concealOncologyConsultationTreatmentPlan(
      profile,
      'treatment-plan-id',
      true,
    );

    expect(
      oncologyConsultationServiceMock.concealOncologyConsultationTreatmentPlan,
    ).toHaveBeenCalledWith(profile, 'treatment-plan-id', true);
  });

  it('addTreatmentPlanConsentSignature(): should call saveOncologyTreatmentPlanConsentSignature service method', async () => {
    const signatureInput = {
      patientConsentSignature: 'John Doe',
      patientConsentSignatureType: 'uploads',
    };

    await resolver.addOncologyTreatmentPlanConsentSignature(
      profile,
      signatureInput,
      'record-id',
      'clinify-id',
    );

    expect(
      oncologyConsultationServiceMock.saveOncologyTreatmentPlanConsentSignature,
    ).toHaveBeenCalledWith(profile, 'record-id', signatureInput);
    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'OncologyConsultationTreatmentPlanUpdated',
      {
        OncologyConsultationTreatmentPlanUpdated: treatmentPlanData,
      },
    );
  });

  it('updateTreatmentPlanConsentSignature(): should call updateOncologyTreatmentPlanConsentSignature service method', async () => {
    const signatureInput = {
      patientConsentSignature: 'John Doe',
      patientConsentSignatureType: 'uploads',
    };

    await resolver.updateOncologyTreatmentPlanConsentSignature(
      profile,
      signatureInput,
      'record-id',
      'clinify-id',
    );

    expect(
      oncologyConsultationServiceMock.updateOncologyTreatmentPlanConsentSignature,
    ).toHaveBeenCalledWith(profile, 'record-id', signatureInput);
    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'OncologyConsultationTreatmentPlanUpdated',
      {
        OncologyConsultationTreatmentPlanUpdated: treatmentPlanData,
      },
    );
  });

  it('deleteOncologyTreatmentPlanConsentSignature(): should call removeOncologyTreatmentPlanConsentSignature service method', async () => {
    await resolver.deleteOncologyTreatmentPlanConsentSignature(
      profile,
      'record-id',
      'clinify-id',
    );

    expect(
      oncologyConsultationServiceMock.removeOncologyTreatmentPlanConsentSignature,
    ).toHaveBeenCalledWith(profile, 'record-id');
    expect(pubSubMock.publish).toHaveBeenCalledWith(
      'OncologyConsultationTreatmentPlanUpdated',
      {
        OncologyConsultationTreatmentPlanUpdated: treatmentPlanData,
      },
    );
  });

  it('updateChemoComment(): should call updateChemoComment service method', async () => {
    const item = {
      ...oncologyChemoDrugFactory.build(),
      oncologyConsultationHistory: {
        profileId: 'profile-id',
      },
    };
    oncologyConsultationServiceMock.updateChemoComment = jest.fn(() => item);
    const mutator = profileFactory.build();

    await resolver.updateChemoComment(
      mutator,
      'chemo-drug-id',
      {
        cycleNumber: 2,
        section: 'post',
        comment: 'comment',
      },
      'clinify-id',
    );

    expect(
      oncologyConsultationServiceMock.updateChemoComment,
    ).toHaveBeenCalledWith(mutator, 'chemo-drug-id', {
      cycleNumber: 2,
      section: 'post',
      comment: 'comment',
    });
    expect(pubSubMock.publish).toHaveBeenCalledWith('MedicationEvent', {
      medication: { profile: { clinifyId: 'clinify-id' } },
      MedicationEvent: 'Updated',
    });
    expect(pubSubMock.publish).toHaveBeenCalledWith('ChemoDrugUpdated', {
      profileId: 'profile-id',
      ChemoDrugUpdated: item,
    });
  });
});
