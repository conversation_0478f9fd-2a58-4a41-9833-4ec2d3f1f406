import { Test, TestingModule } from '@nestjs/testing';
import { OncologyTreatmentPlanResolver } from './oncology-treatment-plan.resolver';
import { treatmentPlanFactory } from '@clinify/__mocks__/factories/consultation.factory';
import { profileFactory } from '@clinify/__mocks__/factories/profile.factory';

describe('OncologyTreatmentPlanResolver', () => {
  let resolver: OncologyTreatmentPlanResolver;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [OncologyTreatmentPlanResolver],
    }).compile();

    resolver = module.get<OncologyTreatmentPlanResolver>(
      OncologyTreatmentPlanResolver,
    );
    jest.clearAllMocks();
  });

  it('getTreatmentPlan(): should get treatment plan', () => {
    const profile = profileFactory.build({ type: 'Patient' });
    const orgUser = profileFactory.build({ type: 'OrganizationDoctor' });

    let treatmentPlanData = treatmentPlanFactory.build({
      conceal: true,
      treatmentPlan: 'Treatment Plan',
    });

    expect(resolver.getTreatmentPlan(profile, treatmentPlanData)).toEqual(null);
    expect(resolver.getTreatmentPlan(orgUser, treatmentPlanData)).toEqual(
      'Treatment Plan',
    );

    treatmentPlanData = treatmentPlanFactory.build({
      conceal: false,
      treatmentPlan: 'Treatment Plan',
    });

    expect(resolver.getTreatmentPlan(profile, treatmentPlanData)).toEqual(
      'Treatment Plan',
    );
    expect(resolver.getTreatmentPlan(orgUser, treatmentPlanData)).toEqual(
      'Treatment Plan',
    );
  });

  it('getObservationNote(): should get treatment plan', () => {
    const profile = profileFactory.build({ type: 'Patient' });
    const orgUser = profileFactory.build({ type: 'OrganizationDoctor' });

    let treatmentPlanData = treatmentPlanFactory.build({
      concealObservationNote: true,
      observationNote: 'Observation Note',
    });

    expect(resolver.getObservationNote(profile, treatmentPlanData)).toEqual(
      null,
    );
    expect(resolver.getObservationNote(orgUser, treatmentPlanData)).toEqual(
      'Observation Note',
    );

    treatmentPlanData = treatmentPlanFactory.build({
      concealObservationNote: false,
      observationNote: 'Observation Note',
    });

    expect(resolver.getObservationNote(profile, treatmentPlanData)).toEqual(
      'Observation Note',
    );
    expect(resolver.getObservationNote(orgUser, treatmentPlanData)).toEqual(
      'Observation Note',
    );
  });
});
