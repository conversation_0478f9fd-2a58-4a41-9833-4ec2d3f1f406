/* eslint-disable max-lines */
import {
  Inject,
  Injectable,
  NotFoundException,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import cloneDeep from 'lodash.clonedeep';
import groupBy from 'lodash.groupby';
import { nanoid } from 'nanoid';
import { DataSource, EntityManager, In, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { v4 as generateUUID } from 'uuid';
import { OncologyConsultationHistoryModel } from '../models/oncology-consultation-history.model';
import { OncologyConsultationRegisterModel } from '../models/oncology-consultation-register.model';
import { OncologyTreatmentPlanModel } from '../models/oncology-treatment-plan.model';
import { OncologyConsultationToInvestigation } from '../models/oncology_consultation_investigation.model';
import { IOncologyConsultationHistoryRepository } from '../repositories/oncology-consultation-history.repository';
import { OncologyConsultationHistoryResponse } from '../responses/oncology-consultation-history.response';
import {
  NewOncologyChemoDrugSingleInput,
  NewOncologyConsultationInput,
  OncologyChemoCommentInput,
  OncologyChemoDrugSingleInput,
  OncologyConsultationInput,
  OncologyConsultationLinkedRecordType,
} from '../validators/oncology-consultation-history.input';
import { OncologyTreatmentPlanInput } from '../validators/oncology-treatment-plan.input';
import { SignatureInput } from '@clinify/admissions/validators/admission.input';
import { BillService } from '@clinify/bills/services/bill.service';
import {
  customDSSerializeInTransaction,
  inTransaction,
  queryDSWithSlave,
} from '@clinify/database';
import { ChemoDiagnosisCycleTemplate } from '@clinify/facility-preferences/interface/chemo-diagnosis.interface';
import { FacilityPreferenceService } from '@clinify/facility-preferences/services/facility-preference.service';
import { HmoClaimService } from '@clinify/hmo-claims/services/hmo-claim.service';
import { InventoryService } from '@clinify/inventory/services/inventory.service';
import { InvestigationModel } from '@clinify/investigation/models/investigation.model';
import { MedicationModel } from '@clinify/medications/models/medication.model';
import { IMedicationRepository } from '@clinify/medications/repositories/medication.repository';
import { MedicationDetailsInput } from '@clinify/medications/validators/medication-details.input';
import { NewMedicationInput } from '@clinify/medications/validators/medication.input';
import { OncologyChemoDrugModel } from '@clinify/oncology-consultation-history/models/oncology-chemo-drug.model';
import { ServiceType } from '@clinify/shared/enums/bill';
import { EventType } from '@clinify/shared/enums/events';
import { InvestigationRequestType } from '@clinify/shared/enums/investigation';
import {
  BankType,
  MedicationOptionType,
} from '@clinify/shared/enums/medication';
import { UserType } from '@clinify/shared/enums/users';
import { getConPeriods } from '@clinify/shared/helper/consultation';
import { LinkService } from '@clinify/shared/services/dashboard-link.service';
import { FilterInput } from '@clinify/shared/validators/filter.input';
import { validateCreation } from '@clinify/shared/validators/validate-record-mutation.validator';
import { ChemoCommentInput } from '@clinify/users/inputs/oncology-history.input';
import {
  OncologyChartType,
  OncologyRegisterChart,
} from '@clinify/users/inputs/oncology-register.input';
import { ProfileModel } from '@clinify/users/models/profile.model';
import { IProfileRepository } from '@clinify/users/repositories/profile.repository';
import {
  generateBillDetails,
  getBillableStatusOf,
  handleSubForSingleBill,
} from '@clinify/utils/helpers/billing.util';
import { PUB_SUB } from '@clinify/utils/subscriptions/pubSub';
import { SubscriptionTypes } from '@clinify/utils/subscriptions/types';

const {
  MedicationAdded,
  MedicationEvent,
  PrescriptionEvent,
  MedicationDetailsAdded,
  MedicationDetailsUpdated,
  MedicationDetailsRemoved,
  MedicationRemoved,
} = SubscriptionTypes;

@Injectable()
export class OncologyConsultationHistoryService {
  constructor(
    @InjectRepository(OncologyConsultationHistoryModel)
    public repository: IOncologyConsultationHistoryRepository,
    @InjectRepository(ProfileModel)
    public profileRepository: IProfileRepository,
    @Inject(forwardRef(() => BillService))
    public billService: BillService,
    @InjectRepository(MedicationModel)
    public medicationRepository: IMedicationRepository,
    readonly entityManager: EntityManager,
    private dataSource: DataSource,
    @Inject(forwardRef(() => HmoClaimService))
    private hmoClaimService: HmoClaimService,
    @InjectRepository(OncologyConsultationToInvestigation)
    public oncologyConsultationToInvestigation: Repository<OncologyConsultationToInvestigation>,
    @InjectRepository(OncologyTreatmentPlanModel)
    public treatmentPlanRepository: Repository<OncologyTreatmentPlanModel>,
    @Inject(forwardRef(() => LinkService)) private linkService: LinkService,
    private readonly facilityPreferenceService: FacilityPreferenceService,
    private inventoryService: InventoryService,
    @Inject(PUB_SUB) private readonly pubSub: RedisPubSub,
  ) {}

  async getOncologyHistory(
    mutator: ProfileModel,
    oncologyId: string,
  ): Promise<OncologyConsultationHistoryModel> {
    const oncologyHistory = await this.repository.getOncologyHistory(
      mutator,
      oncologyId,
    );
    if (!oncologyHistory) {
      throw new NotFoundException('Record Not Found');
    }
    return oncologyHistory;
  }

  async getOncologyHistories(
    mutator: ProfileModel,
    profileId: string,
    filterInput: Partial<FilterInput>,
  ): Promise<OncologyConsultationHistoryResponse> {
    return this.repository.findByProfile(mutator, profileId, filterInput);
  }

  async saveOncologyHistory(
    mutator: ProfileModel,
    input: NewOncologyConsultationInput,
    prescribe?: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const targetProfile = await this.profileRepository.findOne({
      where: { clinifyId: input.clinifyId },
    });
    validateCreation(targetProfile, input);

    const connections = await this.linkService.getLinkedConnections(input);

    return customDSSerializeInTransaction(
      this.dataSource,
      async (manager: EntityManager) => {
        const repositoryTrxnRepo = manager.withRepository(this.repository);
        const oncologyToInvestigationTrxnRepo = manager.withRepository(
          this.oncologyConsultationToInvestigation,
        );
        const treatmentPlanTrxnRepo = manager.withRepository(
          this.treatmentPlanRepository,
        );

        const hmoClaimInput = input.hmoClaim;
        delete input.hmoClaim;

        const chemoDrugsInput = input.oncologyChemoDrugs;
        delete input.oncologyChemoDrugs;

        const { duration, startDate, endDate } = getConPeriods(input);

        const serviceDetails = input.serviceDetails?.map((item) => ({
          ...item,
          reference: item.reference || generateUUID(),
        }));

        const savedOncologyHistory: any = await repositoryTrxnRepo.save({
          ...input,
          ...connections,
          consultationStartDate: startDate,
          consultationEndDate: endDate,
          duration,
          profile: targetProfile,
          hospital: mutator?.hospital,
          creatorName: mutator?.fullName,
          createdBy: mutator,
          serviceDetails,
          subBillRef: input.serviceDetails?.length > 1 ? generateUUID() : null,
        });
        if (hmoClaimInput) {
          const hmoClaim =
            await this.hmoClaimService.createHmoClaimInTransaction(
              manager,
              mutator,
              { ...hmoClaimInput, autoGenerated: true },
              {
                oncologyConsultationHistoryId: savedOncologyHistory.id,
              },
            );
          await manager
            .withRepository(this.repository)
            .update(savedOncologyHistory.id, { hmoClaimId: hmoClaim.id });
          savedOncologyHistory.hmoClaim = hmoClaim;
        }

        const newTreatmentPlan = await treatmentPlanTrxnRepo.save(
          new OncologyTreatmentPlanModel({
            treatmentPlan: input?.treatmentPlan || '',
            patientAdmitted: input?.patientAdmitted || '',
            observationNote: input?.observationNote || '',
            concealObservationNote: input?.concealObservationNote ?? false,
            admissionConsent: input?.admissionConsent || '',
            patientConsentSignatureDateTime: input?.patientConsentSignature
              ? new Date()
              : undefined,
            treatmentGiven: input?.treatmentGiven || '',
            adverseEffectsFollowingTreatment:
              input?.adverseEffectsFollowingTreatment || '',
            stateEffects: input?.stateEffects || '',
            adverseEffectsInvestigated: input?.adverseEffectsInvestigated || '',
            outcomeOfInvestigation: input?.outcomeOfInvestigation || '',
            treatmentStatus: input?.treatmentStatus || '',
            oncologyConsultationHistoryId: savedOncologyHistory.id,
            createdBy: mutator,
            patientConsentSignature: input?.patientConsentSignature,
            patientConsentSignatureType: input?.patientConsentSignatureType,
            creatorName: mutator.fullName,
          }),
        );

        if (connections?.oncology_consultation_investigation?.length > 0) {
          const { oncology_consultation_investigation } = connections;
          oncology_consultation_investigation.map(
            async (cti: OncologyConsultationToInvestigation) => {
              await oncologyToInvestigationTrxnRepo.save(
                new OncologyConsultationToInvestigation({
                  ...cti,
                  oncologyConsultationId: savedOncologyHistory.id,
                }),
              );
            },
          );
        }

        if (chemoDrugsInput?.length) {
          const chemoDrugs = chemoDrugsInput.map(
            (drug) =>
              new OncologyChemoDrugModel({
                ...drug,
                oncologyConsultationHistoryId: savedOncologyHistory.id,
                createdBy: mutator,
                creatorName: mutator.fullName,
              }),
          );
          const drugs = await manager.save(OncologyChemoDrugModel, chemoDrugs);
          savedOncologyHistory.oncologyChemoDrugs = drugs;

          if (prescribe) {
            const medRepo = manager.withRepository(this.medicationRepository);
            const filterd = drugs.filter((d) => d.drugId || d.drugName);
            const medication = await medRepo.saveOncologyPrescribeMedications(
              mutator,
              targetProfile,
              this.composeNewMedicationInput(mutator, targetProfile, filterd, {
                consultationStartDate: startDate,
                facilityAddress: input.facilityAddress,
                facilityName: input.facilityName,
                patientType: input.serviceDetails?.[0]?.patientType,
                paymentType: input.serviceDetails?.[0]?.paymentType,
              }),
            );

            if (medication) {
              for (const drug of filterd) {
                const detail = medication.details.find(
                  ({ medicationName, drugInventoryId }) =>
                    (!!drug.drugName && drug.drugName === medicationName) ||
                    (!!drug.drugId && drug.drugId === drugInventoryId),
                );

                if (detail) {
                  await manager
                    .createQueryBuilder()
                    .update(OncologyChemoDrugModel)
                    .set({
                      medicationDetailsId: detail.id,
                    })
                    .where('id = :id', { id: drug.id })
                    .execute();
                }
              }

              await this.pubSub.publish(MedicationEvent, {
                medication,
                [MedicationEvent]: medication.creatorId,
              });
              await this.pubSub.publish(PrescriptionEvent, {
                medication,
                [PrescriptionEvent]: EventType.ADDED,
              });
              await this.pubSub.publish(MedicationAdded, {
                [MedicationAdded]: medication,
              });
            }
          }
        }

        const oncologyRegister =
          await repositoryTrxnRepo.createOncologyRegister(
            mutator.fullName,
            savedOncologyHistory,
          );

        const response = {
          ...savedOncologyHistory,
          oncologyRegister,
          treatmentPlans: [{ ...newTreatmentPlan, createdBy: mutator }],
        };
        if (mutator.type === UserType.Patient) return response;

        const {
          initialDiagnosisICD10,
          initialDiagnosisICD11,
          initialDiagnosisSNOMED,
        } = response;

        const billDetails = generateBillDetails(
          savedOncologyHistory.id,
          savedOncologyHistory.subBillRef,
          ServiceType.Consultation,
          savedOncologyHistory.serviceDetails,
          [
            {
              diagnosisICD10: initialDiagnosisICD10,
              diagnosisICD11: initialDiagnosisICD11,
              diagnosisSNOMED: initialDiagnosisSNOMED,
            },
          ],
        );

        if (input.saveAsTemplate?.length) {
          const [pref] = await queryDSWithSlave(
            this.dataSource,
            'SELECT id from facility_preferences WHERE hospital_id = $1',
            [mutator.hospitalId],
          );
          // Save Pre Chemo Template
          if (input.saveAsTemplate.includes('pre')) {
            const drugs = chemoDrugsInput
              .filter((d) => d.section === 'pre')
              .map((d) => ({ ...d, ref: nanoid() }));
            const cyclesAvailable = [
              ...new Set(drugs.map(({ cycleNumber }) => cycleNumber)),
            ].sort((a, b) => a - b);
            await this.facilityPreferenceService.saveChemoDiagnosisTemplate(
              mutator,
              {
                type: drugs?.[0]?.chemoDiagnosis,
                combinationName: input.preChemoTemplateName,
                facilityPreferenceId: pref.id,
                section: 'pre',
                cycles: cyclesAvailable.map((cycle) => {
                  const cycleDrugs = drugs
                    .filter((d) => d.cycleNumber === cycle)
                    .sort((a, b) => Number(a.day) - Number(b.day));

                  return {
                    cycleNumber: cycle,
                    drugs: cycleDrugs,
                    investigationDetails:
                      cycleDrugs[0]?.investigationDetails?.map?.((inv) => ({
                        investigationName: inv.investigationName,
                        investigationType: inv.investigationType,
                      })),
                  };
                }) as ChemoDiagnosisCycleTemplate[],
              },
            );
          }

          // Save Chemo Template
          if (input.saveAsTemplate.includes('chemo')) {
            const drugs = chemoDrugsInput
              .filter((d) => d.section === 'chemo')
              .map((d) => ({ ...d, ref: nanoid() }));
            const cyclesAvailable = [
              ...new Set(drugs.map(({ cycleNumber }) => cycleNumber)),
            ];
            await this.facilityPreferenceService.saveChemoDiagnosisTemplate(
              mutator,
              {
                type: drugs?.[0]?.chemoDiagnosis,
                combinationName: input.chemoTemplateName,
                facilityPreferenceId: pref.id,
                section: 'chemo',
                cycles: cyclesAvailable.map((cycle) => {
                  const cycleDrugs = drugs.filter(
                    (d) => d.cycleNumber === cycle,
                  );

                  return {
                    cycleNumber: cycle,
                    drugs: cycleDrugs,
                    investigationDetails:
                      cycleDrugs[0]?.investigationDetails?.map?.((inv) => ({
                        investigationName: inv.investigationName,
                        investigationType: inv.investigationType,
                      })),
                  };
                }) as ChemoDiagnosisCycleTemplate[],
              },
            );
          }

          // Save Post Chemo Template
          if (input.saveAsTemplate.includes('post')) {
            const drugs = chemoDrugsInput
              .filter((d) => d.section === 'post')
              .map((d) => ({ ...d, ref: nanoid() }));
            const cyclesAvailable = [
              ...new Set(drugs.map(({ cycleNumber }) => cycleNumber)),
            ];
            await this.facilityPreferenceService.saveChemoDiagnosisTemplate(
              mutator,
              {
                type: drugs?.[0]?.chemoDiagnosis,
                combinationName: input.postChemoTemplateName,
                facilityPreferenceId: pref.id,
                section: 'post',
                cycles: cyclesAvailable.map((cycle) => {
                  const cycleDrugs = drugs.filter(
                    (d) => d.cycleNumber === cycle,
                  );

                  return {
                    cycleNumber: cycle,
                    drugs: cycleDrugs,
                    investigationDetails:
                      cycleDrugs[0]?.investigationDetails?.map?.((inv) => ({
                        investigationName: inv.investigationName,
                        investigationType: inv.investigationType,
                      })),
                  };
                }) as ChemoDiagnosisCycleTemplate[],
              },
            );
          }
        }

        const generatedBill = await this.billService.generateMultipleBill(
          mutator,
          targetProfile,
          savedOncologyHistory,
          ServiceType.Consultation,
          billDetails,
          manager,
          undefined,
        );

        const billStatus = getBillableStatusOf(generatedBill.billStatus);

        await manager
          .createQueryBuilder()
          .update(OncologyConsultationHistoryModel)
          .set({
            bill: generatedBill,
            billStatus,
            updatedDate: () => 'updated_date',
          })
          .where('id = :id', { id: savedOncologyHistory.id })
          .execute();

        return { ...response, bill: generatedBill, billStatus };
      },
    );
  }

  async updateOncologyHistory(
    mutator: ProfileModel,
    oncologyId: string,
    input: OncologyConsultationInput,
    onlyServiceDetail: boolean,
    prescribe?: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    return customDSSerializeInTransaction(
      this.dataSource,
      async (manager: EntityManager) => {
        const repositoryTrxnRepo = manager.withRepository(this.repository);
        const oncologyToInvestigationTrxnRepo = manager.withRepository(
          this.oncologyConsultationToInvestigation,
        );
        let connections: any = {};

        if (!onlyServiceDetail) {
          // Clear all initial saved oncology-consultation-to-investigation record
          await oncologyToInvestigationTrxnRepo.delete({
            oncologyConsultationId: oncologyId,
          });
          connections = await this.linkService.getLinkedConnections(input);
        }

        const serviceDetails = input.serviceDetails?.map((item) => ({
          ...item,
          reference: item.reference || generateUUID(),
        }));

        const { duration, startDate, endDate } = getConPeriods(input);
        const [updatedOncologyHistory, oldOncologyHistory, drugMeta] =
          await repositoryTrxnRepo.updateOncologyHistory(
            mutator,
            oncologyId,
            {
              ...input,
              serviceDetails,
              ...(!onlyServiceDetail
                ? {
                    consultationEdDate: endDate,
                    consultationStartDate: startDate,
                    duration,
                  }
                : {}),
              ...connections,
            },
            onlyServiceDetail,
          );

        if (!updatedOncologyHistory) {
          throw new NotFoundException('Record Not Found');
        }

        if (Object.keys(drugMeta || {}).length) {
          if (prescribe) {
            const { validChemoDrugs, unsavedChemoDrugs, deletedChemoDrugs } =
              drugMeta;
            let toUpdate = [...validChemoDrugs, ...unsavedChemoDrugs].filter(
              (d) => d.drugId || d.drugName,
            );

            const toDelete = deletedChemoDrugs.filter(
              (d) =>
                d.medicationDetailsId &&
                toUpdate.every(
                  (v) => v.medicationDetailsId !== d.medicationDetailsId,
                ),
            );
            let toCreate = [];
            const medicationDetailId = toUpdate.filter(
              (d) => d.medicationDetailsId,
            )[0]?.medicationDetailsId;
            if (!medicationDetailId) {
              toCreate = toUpdate;
              toUpdate = [];
            }

            const profile = updatedOncologyHistory.profile;
            const medRepo = manager.withRepository(this.medicationRepository);

            if (toUpdate?.length) {
              const detailsInput = this.composeMedicationDetailsInput(
                toUpdate,
                {
                  consultationStartDate: startDate,
                  facilityAddress: input.facilityAddress,
                  facilityName: input.facilityName,
                  patientType: input.serviceDetails?.[0]?.patientType,
                  paymentType: input.serviceDetails?.[0]?.paymentType,
                },
              );

              const medDetails =
                await medRepo.updateOncologyPrescribeMedication(
                  mutator,
                  detailsInput,
                  medicationDetailId,
                );
              await this.pubSub.publish(MedicationEvent, {
                medication: { profile: { clinifyId: input.clinifyId } },
                [MedicationEvent]: EventType.UPDATED,
              });
              await this.pubSub.publish(PrescriptionEvent, {
                medication: medDetails[0].medication,
                [PrescriptionEvent]: EventType.UPDATED,
              });

              for (const drug of toUpdate) {
                const detail = medDetails.find(
                  ({ medicationName, drugInventoryId }) =>
                    (!!drug.drugName && drug.drugName === medicationName) ||
                    (!!drug.drugId && drug.drugId === drugInventoryId),
                );
                if (detail) {
                  await manager
                    .createQueryBuilder()
                    .update(OncologyChemoDrugModel)
                    .set({
                      medicationDetailsId: detail.id,
                    })
                    .where('id = :id', { id: drug.id })
                    .execute();
                }

                if (drug.medicationDetailsId) {
                  await this.pubSub.publish(MedicationDetailsUpdated, {
                    [MedicationDetailsUpdated]: detail,
                  });
                } else {
                  await this.pubSub.publish(MedicationDetailsAdded, {
                    [MedicationDetailsAdded]: {
                      ...detail,
                      medication: medDetails[0].medication,
                    },
                  });
                }
              }
            }
            const deletedIds = [];

            if (toDelete?.length) {
              for (const drug of toDelete) {
                if (deletedIds.includes(drug.medicationDetailsId)) continue;
                deletedIds.push(drug.medicationDetailsId);

                const [med, dispenseDetails] =
                  await medRepo.deleteOncologyPrescribeMedicationDetails(
                    manager,
                    mutator,
                    drug.medicationDetailsId,
                  );

                await Promise.all(
                  dispenseDetails?.map(async (dispenseDetail) => {
                    if (dispenseDetail.bank === BankType.FACILITY) {
                      let inventoryUpdate = [
                        ...(dispenseDetail.dispenseConsumables || []),
                      ];
                      if (dispenseDetail.drugInventoryId) {
                        inventoryUpdate = [
                          ...inventoryUpdate,
                          {
                            name: '',
                            quantityConsumed: dispenseDetail.quantityDispensed,
                            drugInventoryId: dispenseDetail.drugInventoryId,
                          },
                        ];
                      }

                      if (inventoryUpdate.length) {
                        await this.inventoryService.updateInventoryQtys(
                          mutator,
                          inventoryUpdate,
                          'SubtractDispense',
                          undefined,
                          manager,
                        );
                      }
                    }
                  }),
                );

                const detail = med.details.find(
                  (d) => d.id === drug.medicationDetailsId,
                );
                await this.pubSub.publish(PrescriptionEvent, {
                  medication: detail.createdBy,
                  [PrescriptionEvent]: EventType.DELETED,
                });
                await this.pubSub.publish(MedicationDetailsRemoved, {
                  [MedicationDetailsRemoved]: { ...detail, medication: med },
                });
              }
              await this.pubSub.publish(MedicationEvent, {
                medication: { profile: { clinifyId: input.clinifyId } },
                [MedicationEvent]: EventType.UPDATED,
              });
            }
            if (toCreate?.length) {
              const filterd = toCreate.filter((d) => d.drugId || d.drugName);

              const medication = await medRepo.saveOncologyPrescribeMedications(
                mutator,
                profile,
                this.composeNewMedicationInput(mutator, profile, filterd, {
                  consultationStartDate: startDate,
                  facilityAddress: input.facilityAddress,
                  facilityName: input.facilityName,
                  patientType: input.serviceDetails?.[0]?.patientType,
                  paymentType: input.serviceDetails?.[0]?.paymentType,
                }),
              );

              if (medication) {
                for (const details of medication.details) {
                  const chemoDrugs = filterd.find(
                    (d) =>
                      d.drugName === details.medicationName ||
                      d.drugId === details.drugInventoryId,
                  );
                  if (chemoDrugs) {
                    await manager
                      .createQueryBuilder()
                      .update(OncologyChemoDrugModel)
                      .set({
                        medicationDetailsId: details.id,
                      })
                      .where('id = :id', { id: chemoDrugs.id })
                      .execute();
                  }
                }
                for (const drug of filterd) {
                  const detail = medication.details.find(
                    ({ medicationName, drugInventoryId }) =>
                      (!!drug.drugName && drug.drugName === medicationName) ||
                      (!!drug.drugId && drug.drugId === drugInventoryId),
                  );

                  if (detail) {
                    await manager
                      .createQueryBuilder()
                      .update(OncologyChemoDrugModel)
                      .set({
                        medicationDetailsId: detail.id,
                      })
                      .where('id = :id', { id: drug.id })
                      .execute();
                  }
                }

                await this.pubSub.publish(MedicationEvent, {
                  medication,
                  [MedicationEvent]: medication.creatorId,
                });
                await this.pubSub.publish(PrescriptionEvent, {
                  medication,
                  [PrescriptionEvent]: EventType.ADDED,
                });
                await this.pubSub.publish(MedicationAdded, {
                  [MedicationAdded]: medication,
                });
              }
            }
          }
        }
        if (connections?.oncology_consultation_investigation?.length > 0) {
          const { oncology_consultation_investigation } = connections;
          oncology_consultation_investigation.map(
            async (cti: OncologyConsultationToInvestigation) => {
              await oncologyToInvestigationTrxnRepo.save(
                new OncologyConsultationToInvestigation({
                  ...cti,
                  oncologyConsultationId: oncologyId,
                }),
              );
            },
          );
        }

        const oncologyRegister =
          await repositoryTrxnRepo.createOncologyRegister(
            mutator.fullName,
            updatedOncologyHistory,
            oldOncologyHistory,
          );

        const response = { ...updatedOncologyHistory, oncologyRegister };

        const {
          initialDiagnosisICD10,
          initialDiagnosisICD11,
          initialDiagnosisSNOMED,
        } = response;

        const { billRefToDelete, billDetails } = handleSubForSingleBill(
          updatedOncologyHistory as any,
          oldOncologyHistory,
          ServiceType.Consultation,
          undefined,
          [
            {
              diagnosisICD10: initialDiagnosisICD10,
              diagnosisICD11: initialDiagnosisICD11,
              diagnosisSNOMED: initialDiagnosisSNOMED,
            },
          ],
        );

        const updatedBill = await this.billService.updateMultipleBill(
          mutator,
          updatedOncologyHistory.profile,
          updatedOncologyHistory,
          ServiceType.Consultation,
          updatedOncologyHistory.billId,
          billDetails,
          [billRefToDelete],
          manager,
          true,
        );

        return {
          ...response,
          ...(updatedBill
            ? {
                bill: updatedBill,
                billStatus: getBillableStatusOf(updatedBill.billStatus),
              }
            : {}),
        };
      },
    );
  }

  async updateOncologyRegister(
    mutator: ProfileModel,
    id: string,
    chartType: OncologyChartType,
    input: OncologyRegisterChart,
  ): Promise<OncologyConsultationRegisterModel> {
    return this.repository.updateOncologyRegister(
      mutator,
      id,
      chartType,
      input,
    );
  }

  async deleteOncologyHistory(
    mutator: ProfileModel,
    oncologyIds: string[],
  ): Promise<OncologyConsultationHistoryModel[]> {
    return inTransaction(this.entityManager, async (manager) => {
      const repositoryTrxnRepo = manager.withRepository(this.repository);
      const deleteOncologyHistories =
        await repositoryTrxnRepo.deleteOncologyHistory(mutator, oncologyIds);

      const billItemsRefsToDelete: string[] = [];

      deleteOncologyHistories.forEach((oncologyHistory) => {
        if (oncologyHistory?.subBillRef) {
          billItemsRefsToDelete.push(oncologyHistory.subBillRef);
        }
        billItemsRefsToDelete.push(oncologyHistory.id);
        if (oncologyHistory.hmoClaim) {
          this.hmoClaimService.flagHmoClaimDeletedInTransaction(
            manager,
            mutator,
            oncologyHistory.hmoClaim.id,
          );
        }
      });

      await this.billService.deleteAutoGeneratedBillItems(
        mutator,
        OncologyConsultationHistoryModel,
        billItemsRefsToDelete,
        manager,
        ServiceType.Consultation,
      );
      const drugs = deleteOncologyHistories
        .map((d) => d.oncologyChemoDrugs)
        .flat()
        .filter((item) => !!item?.medicationDetailsId);

      if (drugs?.length && drugs.some((d) => d.medicationDetailsId)) {
        const medRepo = manager.withRepository(this.medicationRepository);
        await this.pubSub.publish(MedicationEvent, {
          medication: {
            profile: {
              clinifyId: deleteOncologyHistories[0].profile?.clinifyId,
            },
          },
          [MedicationEvent]: EventType.UPDATED,
        });
        const [med, dispenseDetails] =
          await medRepo.deleteOncologyPrescribeMedication(
            manager,
            mutator,
            drugs[0].medicationDetailsId,
          );

        await Promise.all(
          dispenseDetails?.map(async (dispenseDetail) => {
            if (dispenseDetail.bank === BankType.FACILITY) {
              let inventoryUpdate = [
                ...(dispenseDetail.dispenseConsumables || []),
              ];
              if (dispenseDetail.drugInventoryId) {
                inventoryUpdate = [
                  ...inventoryUpdate,
                  {
                    name: '',
                    quantityConsumed: dispenseDetail.quantityDispensed,
                    drugInventoryId: dispenseDetail.drugInventoryId,
                  },
                ];
              }

              if (inventoryUpdate.length) {
                await this.inventoryService.updateInventoryQtys(
                  mutator,
                  inventoryUpdate,
                  'SubtractDispense',
                  undefined,
                  manager,
                );
              }
            }

            const recordBillType =
              dispenseDetail.option === MedicationOptionType.M
                ? ServiceType.Medication
                : ServiceType.Consumable;

            await this.billService.deleteAutoGeneratedBillItems(
              mutator,
              MedicationModel,
              [dispenseDetail.id],
              manager,
              recordBillType,
              true,
            );
          }),
        );

        await this.pubSub.publish(MedicationEvent, {
          medication: med,
          [MedicationEvent]: EventType.DELETED,
        });
        await this.pubSub.publish(MedicationRemoved, {
          [MedicationRemoved]: [med],
        });
      }
      return deleteOncologyHistories;
    });
  }

  async archiveOncologyHistory(
    mutator: ProfileModel,
    oncologyIds: string[],
    archive: boolean,
  ): Promise<OncologyConsultationHistoryModel[]> {
    return this.repository.archiveOncologyHistory(
      mutator,
      oncologyIds,
      archive,
      this.billService,
    );
  }

  async concealConsultationComplaint(
    profile: ProfileModel,
    oncologyId: string,
    concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const conceal = await this.repository.concealOncologyConsultationFields(
      profile,
      oncologyId,
      concealStatus,
      'concealComplaint',
    );
    return conceal;
  }

  async concealConsultationComplaintHistory(
    profile: ProfileModel,
    oncologyId: string,
    concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const conceal = await this.repository.concealOncologyConsultationFields(
      profile,
      oncologyId,
      concealStatus,
      'concealComplaintHistory',
    );
    return conceal;
  }

  async concealConsultationSystemReview(
    profile: ProfileModel,
    oncologyId: string,
    concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const conceal = await this.repository.concealOncologyConsultationFields(
      profile,
      oncologyId,
      concealStatus,
      'concealSystemReview',
    );
    return conceal;
  }

  async concealConsultationPhysicalExam(
    profile: ProfileModel,
    oncologyId: string,
    concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const conceal = await this.repository.concealOncologyConsultationFields(
      profile,
      oncologyId,
      concealStatus,
      'concealPhysicalExam',
    );
    return conceal;
  }

  async concealConsultationAudiometry(
    profile: ProfileModel,
    oncologyId: string,
    concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const conceal = await this.repository.concealOncologyConsultationFields(
      profile,
      oncologyId,
      concealStatus,
      'concealAudiometry',
    );
    return conceal;
  }

  async concealConsultationHealthEducation(
    profile: ProfileModel,
    oncologyId: string,
    concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const conceal = await this.repository.concealOncologyConsultationFields(
      profile,
      oncologyId,
      concealStatus,
      'concealHealthEducation',
    );
    return conceal;
  }

  async concealOncologyConsultationChemoNote(
    profile: ProfileModel,
    oncologyId: string,
    concealStatus: boolean,
  ): Promise<OncologyConsultationHistoryModel> {
    const conceal = await this.repository.concealOncologyConsultationFields(
      profile,
      oncologyId,
      concealStatus,
      'concealChemoNote',
    );
    return conceal;
  }

  addChemoDrug(profile: ProfileModel, input: NewOncologyChemoDrugSingleInput) {
    return this.repository.addChemoDrug(profile, input);
  }

  async updateChemoDrug(
    profile: ProfileModel,
    input: OncologyChemoDrugSingleInput,
  ) {
    return this.repository.updateChemoDrug(profile, input);
  }

  async deleteChemoDrug(id: string) {
    return this.repository.deleteChemoDrug(id);
  }

  async administerChemoDrug(
    mutator: ProfileModel,
    id: string,
    period: string,
    status: boolean,
  ) {
    const chemoDrug = await this.entityManager.findOneOrFail(
      OncologyChemoDrugModel,
      { where: { id }, relations: ['oncologyConsultationHistory'] },
    );
    const _register = cloneDeep(chemoDrug.administrationRegister);
    const periodIdx = _register.findIndex((reg) => reg.period === period);
    _register[periodIdx] = {
      ..._register[periodIdx],
      administeredBy: status ? mutator.fullName : null,
      administratorId: status ? mutator.id : null,
      administrationDateTime: status ? new Date() : null,
    };
    const _update: QueryDeepPartialEntity<OncologyChemoDrugModel> = {
      administrationRegister: _register,
    };
    await this.entityManager.update<OncologyChemoDrugModel>(
      OncologyChemoDrugModel,
      { id },
      _update,
    );

    return {
      ...chemoDrug,
      administrationRegister: _register,
    };
  }

  async linkRecordsToOncologyConsultation(
    updatedBy: ProfileModel,
    oncologyId: string,
    recordType: OncologyConsultationLinkedRecordType,
    recordIds: string[],
  ): Promise<OncologyConsultationHistoryModel> {
    let oncologyConsultationToInvestigations;
    const oncologyConsultation = await this.repository
      .findOneOrFail({
        where: { id: oncologyId },
        join: {
          alias: 'oncologyConsultation',
        },
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    if (recordType === OncologyConsultationLinkedRecordType.LabTest) {
      const investigations = await this.repository.manager.find(
        InvestigationModel,
        { where: { id: In(recordIds) } },
      );
      await this.oncologyConsultationToInvestigation.delete({
        oncologyConsultationId: oncologyId,
        type: InvestigationRequestType.Laboratory,
      });
      oncologyConsultationToInvestigations = await investigations.map(
        async (investigation) => {
          const consultationToInvestigation =
            await this.oncologyConsultationToInvestigation.save({
              oncologyConsultationId: oncologyId,
              oncologyConsultation,
              investigation,
              investigationId: investigation.id,
              type: InvestigationRequestType.Laboratory,
            });

          return consultationToInvestigation.id;
        },
      );

      return oncologyConsultation;
    }
    if (recordType === OncologyConsultationLinkedRecordType.Radiology) {
      const investigations = await this.repository.manager.find(
        InvestigationModel,
        { where: { id: In(recordIds) } },
      );
      await this.oncologyConsultationToInvestigation.delete({
        oncologyConsultationId: oncologyId,
        type: InvestigationRequestType.Radiology,
      });
      oncologyConsultationToInvestigations = await investigations.map(
        async (investigation) => {
          const consultationToInvestigation =
            await this.oncologyConsultationToInvestigation.save({
              oncologyConsultationId: oncologyId,
              oncologyConsultation,
              investigation,
              investigationId: investigation.id,
              type: InvestigationRequestType.Radiology,
            });

          return consultationToInvestigation.id;
        },
      );

      return oncologyConsultation;
    }

    if (recordType === OncologyConsultationLinkedRecordType.Investigation) {
      const investigations = await this.repository.manager.find(
        InvestigationModel,
        { where: { id: In(recordIds) } },
      );
      await this.oncologyConsultationToInvestigation.delete({
        oncologyConsultationId: oncologyId,
      });
      oncologyConsultationToInvestigations = await investigations.map(
        async (investigation) => {
          const consultationToInvestigation =
            await this.oncologyConsultationToInvestigation.save({
              oncologyConsultationId: oncologyId,
              oncologyConsultation,
              investigation,
              investigationId: investigation.id,
              type: investigation.requestType,
            });

          return consultationToInvestigation.id;
        },
      );

      return oncologyConsultation;
    }

    return this.repository.linkRecordsToOncologyConsultation(
      oncologyConsultation,
      oncologyConsultationToInvestigations || recordIds,
      updatedBy,
      recordType,
    );
  }

  async getLinkedRadiologyInvestigationRecords(
    profile: ProfileModel,
    oncologyId: string,
  ): Promise<InvestigationModel[]> {
    const linkedRadiology =
      await this.repository.getLinkedRadiologyInvestigationRecords(
        profile,
        oncologyId,
      );
    return linkedRadiology;
  }

  async getLinkedLaboratoryInvestigationRecords(
    profile: ProfileModel,
    oncologyId: string,
  ): Promise<InvestigationModel[]> {
    const linkedLaboratory =
      await this.repository.getLinkedLaboratoryInvestigationRecords(
        profile,
        oncologyId,
      );
    return linkedLaboratory;
  }

  async getLinkedInvestigationRecords(
    profile: ProfileModel,
    oncologyId: string,
  ): Promise<InvestigationModel[]> {
    const linkedInvestigation =
      await this.repository.getLinkedInvestigationRecords(profile, oncologyId);
    return linkedInvestigation;
  }
  composeNewMedicationInput(
    mutator: ProfileModel,
    targetProfile: ProfileModel,
    drugs: OncologyChemoDrugModel[],
    input: {
      consultationStartDate: Date;
      facilityName: string;
      facilityAddress: string;
      patientType?: string;
      paymentType?: string;
    },
  ) {
    const medication: NewMedicationInput = {
      clinifyId: targetProfile.clinifyId,
      additionalNote: '',
      concealAdditionalNote: true,
      totalQuantity: null,
      totalPrice: null,
      hospitalName: input.facilityName,
      hospitalAddress: input.facilityAddress,
      prescribedBy: mutator.fullName,
      documentUrl: [],
      interval: null,
      intervalUnit: '',
      department: 'Administration',
      rank: null,
      specialty: null,
      setReminder: false,
      reminderStartDate: null,
      reminderEndDate: null,
      medicationStartTime: null,
      medicationEndTime: null,
      remindMe: null,
      dispenseDetails: null,
      concealAdministrationNote: true,
      refillNumber: 0,
      discontinue: null,
      details: this.composeMedicationDetailsInput(drugs, input),
    };
    return medication;
  }
  composeMedicationDetailsInput(
    drugs: OncologyChemoDrugModel[],
    input: {
      consultationStartDate: Date;
      facilityName: string;
      facilityAddress: string;
      patientType?: string;
      paymentType?: string;
    },
  ) {
    const groupByDrug = groupBy(drugs, 'drugName');
    return Object.entries(groupByDrug).map(
      ([_, drugs]: [string, OncologyChemoDrugModel[]]) => {
        const [dosage, unit] = drugs.reduce(
          (acc, d) => {
            const [dosage, unit] = (d.dosage || '').split('::');
            acc[0].push(dosage);
            acc[1].push(unit);
            return acc;
          },
          [[], []],
        );

        const _diagnosis = drugs?.filter((d) => !!d.chemoDiagnosis);
        let clinicalDiagnosis = [];
        if (_diagnosis?.length) {
          clinicalDiagnosis = Array.from(
            new Set(_diagnosis?.map((_d) => _d.chemoDiagnosis)),
          );
        }

        return {
          id: drugs[0].medicationDetailsId || undefined,
          medicationType: 'Chemo Regimen',
          quantity: drugs
            .map((d) => d.totalDose)
            .filter(Boolean)
            .join(', '),
          medicationName: drugs[0].drugName,
          drugInventoryId: drugs[0].drugId,
          dosage: dosage.filter((item) => !!item).join(', '),
          dosageUnit: unit.filter((item) => !!item).join(', '),
          administrationMethod:
            drugs
              .map((d) => d.route)
              .filter(Boolean)
              .join(', ') || '',
          frequency: drugs
            .map((d) => d.frequency)
            .filter(Boolean)
            .join(', '),
          diagnosis: clinicalDiagnosis?.map((_diag) => ({
            diagnosisICD10: _diag,
            diagnosisICD11: null,
            diagnosisSNOMED: null,
          })),
          prescriptionNote: drugs
            ?.filter((d) => !!d.note)
            ?.map((_d) => _d.note)
            .join(', '),
          unitPrice: '0',
          datePrescribed: input.consultationStartDate,
          bank: BankType.FACILITY,
          option: MedicationOptionType.M,
          inventoryClass: drugs[0].inventoryClass || 'External',
          duration: '',
          medicationCategory: '',
          purpose: '',
          type: '',
          startDate: null,
          endDate: null,
          discontinue: null,
          concealPrescriptionNote: true,
          refillNumber: 0,
          provider: null,
          priceDetails: {
            pricePerUnit: '0',
            patientType: input.patientType,
            paymentType: input.paymentType,
            type: 'DRUG',
            name: drugs[0].drugName,
            quantity: drugs.reduce((acc, d) => acc + +(d.quantity || '0'), 0),
          },
          hmoClaim: null,
        };
      },
    ) as unknown as MedicationDetailsInput[];
  }

  async getChemoDrugsByMedicationDetailsId(
    medicationDetailsId: string,
  ): Promise<OncologyChemoDrugModel[]> {
    const drugs = await this.entityManager.find(OncologyChemoDrugModel, {
      relations: ['oncologyConsultationHistory'],
      where: { medicationDetailsId },
    });
    return drugs;
  }

  async oncologyConsultationTreatmentPlans(
    profile: ProfileModel,
    consultationId: string,
  ): Promise<OncologyTreatmentPlanModel[]> {
    const treatmentPlans =
      await this.repository.oncologyConsultationTreatmentPlans(
        profile,
        consultationId,
      );
    return treatmentPlans;
  }

  async addOncologyConsultationTreatmentPlan(
    mutator: ProfileModel,
    consultationId: string,
    input: OncologyTreatmentPlanInput,
  ): Promise<OncologyTreatmentPlanModel> {
    const treatmentPlan =
      await this.repository.addOncologyConsultationTreatmentPlan(
        mutator,
        consultationId,
        input,
      );

    return treatmentPlan;
  }

  async updateOncologyConsultationTreatmentPlan(
    mutator: ProfileModel,
    id: string,
    input: OncologyTreatmentPlanInput,
  ): Promise<OncologyTreatmentPlanModel> {
    const treatmentPlan =
      await this.repository.updateOncologyConsultationTreatmentPlan(
        mutator,
        id,
        input,
      );

    return treatmentPlan;
  }

  async deleteOncologyConsultationTreatmentPlan(
    mutator: ProfileModel,
    id: string,
  ): Promise<OncologyTreatmentPlanModel> {
    const treatmentPlan =
      await this.repository.deleteOncologyConsultationTreatmentPlan(
        mutator,
        id,
      );

    return treatmentPlan;
  }

  async concealOncologyConsultationTreatmentPlan(
    profile: ProfileModel,
    treatmentPlanId: string,
    concealStatus: boolean,
  ): Promise<OncologyTreatmentPlanModel> {
    const conceal = await this.repository.concealOncologyTreatmentPlanFields(
      profile,
      treatmentPlanId,
      concealStatus,
      'conceal',
    );
    return conceal;
  }

  async concealOncologyTreatmentPlanObservationNote(
    profile: ProfileModel,
    treatmentPlanId: string,
    concealStatus: boolean,
  ): Promise<OncologyTreatmentPlanModel> {
    const conceal = await this.repository.concealOncologyTreatmentPlanFields(
      profile,
      treatmentPlanId,
      concealStatus,
      'concealObservationNote',
    );
    return conceal;
  }

  async saveOncologyTreatmentPlanConsentSignature(
    profile: ProfileModel,
    treatmentPlanId: string,
    signatureInput: SignatureInput,
  ): Promise<OncologyTreatmentPlanModel> {
    const { patientConsentSignature, patientConsentSignatureType } =
      signatureInput;
    return await this.repository.updateOncologyTreatmentPlanConsentSignature(
      profile,
      treatmentPlanId,
      patientConsentSignature,
      patientConsentSignatureType,
      true,
    );
  }

  async updateOncologyTreatmentPlanConsentSignature(
    profile: ProfileModel,
    treatmentPlanId: string,
    signatureInput: SignatureInput,
  ): Promise<OncologyTreatmentPlanModel> {
    const { patientConsentSignature, patientConsentSignatureType } =
      signatureInput;
    return await this.repository.updateOncologyTreatmentPlanConsentSignature(
      profile,
      treatmentPlanId,
      patientConsentSignature,
      patientConsentSignatureType,
    );
  }

  async removeOncologyTreatmentPlanConsentSignature(
    profile: ProfileModel,
    treatmentPlanId: string,
  ): Promise<OncologyTreatmentPlanModel> {
    const patientConsentSignature = '';
    const patientConsentSignatureType = '';

    return await this.repository.updateOncologyTreatmentPlanConsentSignature(
      profile,
      treatmentPlanId,
      patientConsentSignature,
      patientConsentSignatureType,
    );
  }

  async updateChemoComment(
    mutator: ProfileModel,
    chemoDrugId: string,
    input: OncologyChemoCommentInput,
  ): Promise<OncologyChemoDrugModel> {
    const chemoDrug = await this.entityManager
      .findOneOrFail(OncologyChemoDrugModel, {
        where: { id: chemoDrugId },
        relations: ['oncologyConsultationHistory'],
      })
      .catch(() => {
        throw new NotFoundException('Record Not Found');
      });

    const oncologyHistory = chemoDrug?.oncologyConsultationHistory;

    const oncologyComments: ChemoCommentInput[] =
      oncologyHistory.chemoComments || [];

    const { cycleNumber, section, comment } = input;

    let commentIndex = -1;
    if (oncologyComments.length) {
      commentIndex = oncologyComments.findIndex(
        (_item) =>
          _item?.cycleNumber === cycleNumber && _item?.section === section,
      );
    }

    if (commentIndex === -1) {
      oncologyComments.push({
        ...input,
        creatorId: mutator.id,
        creatorName: mutator.fullName,
        createdDate: new Date(),
      });
    } else {
      oncologyComments[commentIndex] = {
        ...oncologyComments[commentIndex],
        comment,
        lastModifierId: mutator.id,
        lastModifierName: mutator.fullName,
        updatedDate: new Date(),
      };
    }

    await this.repository.save({
      ...oncologyHistory,
      chemoComments: oncologyComments,
    });

    return {
      ...chemoDrug,
      oncologyConsultationHistory: {
        ...oncologyHistory,
        chemoComments: oncologyComments,
      },
    };
  }
}
