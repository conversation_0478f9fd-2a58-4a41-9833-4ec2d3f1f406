# Project Guidelines

## Project Structure
This is a NestJS TypeScript API project for Clinify with the following key components:
- **Source code**: Located in `src/` directory
- **Tests**: Unit tests (`.spec.ts`) and E2E tests (`.e2e.spec.ts`) alongside source files
- **Configuration**: Uses TypeORM for database, Jest for testing, GraphQL with Apollo
- **Build output**: Generated in `dist/` directory

## Testing Instructions
**IMPORTANT**: When running tests on this project, use `jest` command directly instead of `npm test` to avoid build time.

Available test commands:
- `jest` - Run tests directly (recommended for faster execution)
- `yarn test:unit` - Alternative fast test command (also uses jest directly)
- `npm test` or `yarn test` - Full test suite including migrations (slower, avoid for development)

## Code Style
- Use TypeScript with strict typing
- Follow NestJS conventions and decorators
- Use ESLint and Prettier for code formatting
- Run `yarn fix` to auto-fix formatting issues

## Development Workflow
- Use `yarn start:dev` for development with hot reload
- Build the project with `yarn build` if needed
- Database migrations are handled separately via TypeORM CLI commands

## Migration File Creation
**IMPORTANT**: When asked to create a migration file, follow this workflow:

1. **First step**: Handle migration file naming
   - If no migration file name is provided in the initial request, prompt for one
   - If the user specifies "*" (asterisk) as the migration file name, generate a recommended migration file name based on the operation/content of the script (e.g., "AddUserEmailColumn", "CreateAppointmentsTable", "UpdateProfileConstraints")
   - **Format**: Migration file names should use PascalCase format
2. **Generate migration**: Use the `migration:create` command to generate an empty migration file
   - Command format: Replace 'add-name' in the migration:create script with the specified migration file name
   - Example: `yarn migration:create` (where 'add-name' gets replaced with the actual migration name)
3. **Determine migration content**: Create the migration file content based on the user's prompt/request rather than using automated generation
   - Manually craft the migration script according to the specific requirements provided in the prompt
   - Avoid using `gen-migration` command as it can produce overcrowded content
4. **File naming**: Migration file names will always have the `.ts` file extension unless otherwise stated
5. **Location**: Migration files are created in `src/database/migrations/` directory

## Repository Testing with Fixtures
**IMPORTANT**: When writing repository tests, always create and use fixtures for affected records/schemas/tables and perform tests based on fixture data.

### Using Existing Fixtures
1. **Check for existing fixtures**: Look in `src/utils/tests/` directory for existing fixture files (e.g., `user.fixtures.ts`, `hospital.fixtures.ts`)
2. **Import fixtures**: Use the `@fixtures/*` alias to import fixture functions (e.g., `import { createUsers } from '@fixtures/user.fixtures'`)
3. **Create test data**: Call fixture functions in `beforeAll` or `beforeEach` hooks with proper dependencies
   - Pass `EntityManager` as first parameter
   - Specify count as second parameter
   - Use optional parameters for customization (hospital, hmoProvider, etc.)

### Creating New Fixtures
If no existing fixture generator exists for a schema/table:

1. **Create fixture file**: Create a new file in `src/utils/tests/` following the naming pattern `{model-name}.fixtures.ts`
2. **Follow established patterns**:
   - Import `Chance` library for realistic random data generation
   - Import `EntityManager` from TypeORM
   - Import related models and factories from `@mocks/factories/`
   - Export a function named `create{ModelName}s` (plural)
3. **Function signature**: `async (manager: EntityManager, count = 1, ...optionalParams) => Promise<ModelType[]>`
4. **Implementation**:
   - Use factory patterns for base data generation
   - Handle relationships with other entities properly
   - Create related entities as needed (details, profiles, etc.)
   - Use realistic data that reflects business logic
   - Return array of created entities

### Writing Detailed Repository Tests
1. **Comprehensive setup**: Use TestingModule with TypeORM configuration and real database connections
2. **Fixture dependencies**: Create fixtures in proper dependency order (e.g., hospitals → users → profiles)
3. **Multiple scenarios**: Test various user types, edge cases, and business logic scenarios
4. **Detailed assertions**: Verify not just existence but also relationships, computed fields, and business rules
5. **Clean test data**: Use fixtures to create consistent, predictable test scenarios
