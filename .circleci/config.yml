version: 2.1
orbs:
  codecov: codecov/codecov@1.0.2
  aws-ecr: circleci/aws-ecr@7.0.0
  eb: circleci/aws-elastic-beanstalk@1.0.0
  aws-cli: circleci/aws-cli@3.1
  aws-ecs: circleci/aws-ecs@3.2.0

attach_workspace: &attach_workspace
  attach_workspace:
    at: ~/clinify-api

persist_to_workspace: &persist_to_workspace
  persist_to_workspace:
    root: .
    paths: 
      - .

jobs:
  build-and-test:
    working_directory: ~/clinify-api
    # The primary container is an instance of the first image listed.
    # The job's commands run in this container.
    docker:
      - image: cimg/node:18.20.8
        environment:
          PGHOST: 127.0.0.1
          PGUSER: circleci
          NODE_ENV: test
      - image: cimg/postgres:13.10
        environment:
          POSTGRES_USER: circleci
          POSTGRES_DB: circle_test
          POSTGRES_PASSWORD: password
          SECRET: $SECRET
    resource_class: xlarge
    steps:
      - checkout
      - restore_cache:
          key: v2-dependencies-{{ checksum "yarn.lock" }}
      - run:
          name: Install dependencies
          command: yarn install --ignore-engines
      - save_cache:
          name: Save YARN Package Cache
          key: v1-dependencies-{{ checksum "yarn.lock" }}
          paths:
            - ./node_modules
      # Lint the source code
      - run:
          name: Linting
          command: yarn lint
      # Run tests
      - run:
          name: Run Tests and Collect Coverage Reports
          command: |
            mkdir -p test-results/jest
            yarn test --maxWorkers=4  --coverage
      - store_artifacts:
          path: coverage
          destination: coverage
      - *persist_to_workspace
 
  #### STAGING ####

  update-ecs-task-staging:
    working_directory:  ~/clinify-api
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - *attach_workspace
      - aws-cli/setup
      - aws-ecs/update-task-definition:
          family: api-staging
          container-image-name-updates: container=api,tag=staging-latest

  update-ecs-task-staging-migration:
    working_directory:  ~/clinify-api
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - *attach_workspace
      - aws-cli/setup
      - aws-ecs/update-task-definition:
          family: api-staging-migration
          container-image-name-updates: container=api,tag=staging-latest
  
  migration-staging:
    working_directory:  ~/clinify-api
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - *attach_workspace
      - aws-cli/setup
      - aws-ecs/run-task:
          task-definition: api-staging-migration
          cluster: Backend-API
          run-task-output: out-migration-staging.json
          subnet-ids: subnet-0e05f6c5635b5295e,subnet-0dce6ee4d2a20472e
      - run:
          name: 'Check task status'
          command: |
            task_arn=$(jq -r '.tasks[0].taskArn' out-migration-staging.json)
            task_id=$(basename "$task_arn")
            aws ecs wait tasks-stopped --cluster Backend-API --tasks "$task_id"
            ecs_exit_code=$(aws ecs describe-tasks --cluster Backend-API --tasks "$task_id" | jq -r '.tasks[0].containers[0].exitCode')
            echo "$ecs_exit_code $task_id"
            if [ "$ecs_exit_code" -ne 0 ]; then
              echo "ECS task failed with exit code $ecs_exit_code."
              exit 1
            fi
            echo "migration complete."
  
  update-ecs-service-staging:
    working_directory:  ~/clinify-api
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - *attach_workspace
      - aws-cli/setup
      - aws-ecs/update-service:
          cluster: Backend-API
          family: api-staging
          service-name: api-staging
          skip-task-definition-registration: true

  #### PRODUCTION ####

  update-ecs-task-prod:
    working_directory:  ~/clinify-api
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - *attach_workspace
      - aws-cli/setup
      - aws-ecs/update-task-definition:
          family: api-prod
          container-image-name-updates: container=api,tag=master-latest
  
  update-ecs-task-prod-migration:
    working_directory:  ~/clinify-api
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - *attach_workspace
      - aws-cli/setup
      - aws-ecs/update-task-definition:
          family: api-prod-migration
          container-image-name-updates: container=api,tag=master-latest

  migration-prod:
    working_directory:  ~/clinify-api
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - *attach_workspace
      - aws-cli/setup
      - aws-ecs/run-task:
          task-definition: api-prod-migration
          cluster: Backend-API
          run-task-output: out-migration-prod.json
          subnet-ids: subnet-0e05f6c5635b5295e,subnet-0dce6ee4d2a20472e
      - run:
          name: 'Check task status'
          command: |
            task_arn=$(jq -r '.tasks[0].taskArn' out-migration-prod.json)
            task_id=$(basename "$task_arn")
            aws ecs wait tasks-stopped --cluster Backend-API --tasks "$task_id"
            ecs_exit_code=$(aws ecs describe-tasks --cluster Backend-API --tasks "$task_id" | jq -r '.tasks[0].containers[0].exitCode')
            echo "$ecs_exit_code $task_id"
            if [ "$ecs_exit_code" -ne 0 ]; then
              echo "ECS task failed with exit code $ecs_exit_code."
              exit 1
            fi
            echo "migration complete."
  
  update-ecs-service-prod:
    working_directory:  ~/clinify-api
    docker:
      - image: cimg/python:3.10
    steps:
      - checkout
      - *attach_workspace
      - aws-cli/setup
      - aws-ecs/update-service:
          cluster: Backend-API
          family: api-prod
          service-name: api-prod
          skip-task-definition-registration: true

executors:
  custom:
    machine:
      image: ubuntu-2204:current
    resource_class: xlarge

workflows:
  version: 2
  test_build_deploy:
    jobs:
      - build-and-test
      - aws-ecr/build-and-push-image:
          executor: custom
          account-url: AWS_ECR_ACCOUNT_URL
          aws-access-key-id: AWS_ACCESS_KEY_ID
          aws-secret-access-key: AWS_SECRET_ACCESS_KEY
          create-repo: false
          no-output-timeout: 20m
          region: AWS_REGION
          repo: clinify-api
          skip-when-tags-exist: false
          tag: $CIRCLE_BRANCH-latest,$CIRCLE_BRANCH-$(git rev-parse --short=10 HEAD)
          pre-steps:
            - run:
                name: Set SENTRY_PROJECT variable
                command: |
                  if [ "${CIRCLE_BRANCH}" == "master" ]; then
                    echo 'export SENTRY_PROJECT="prod-api"' >> $BASH_ENV
                  else
                    echo 'export SENTRY_PROJECT="staging-api"' >> $BASH_ENV
                  fi
                  source $BASH_ENV
          requires:
            - build-and-test
          filters:
            branches:
              only:
                - staging
                - master
      #### Build offline version
      - aws-ecr/build-and-push-image:
          executor: custom
          name: build-push-offline-image
          account-url: AWS_ECR_ACCOUNT_URL
          aws-access-key-id: AWS_ACCESS_KEY_ID
          aws-secret-access-key: AWS_SECRET_ACCESS_KEY
          create-repo: false
          no-output-timeout: 20m
          region: AWS_REGION
          repo: clinify-api-local
          skip-when-tags-exist: false
          tag: latest
          pre-steps:
            - run:
                name: Set SENTRY_PROJECT variable
                command: |
                  if [ "${CIRCLE_BRANCH}" == "master" ]; then
                    echo 'export SENTRY_PROJECT="prod-api"' >> $BASH_ENV
                  else
                    echo 'export SENTRY_PROJECT="staging-api"' >> $BASH_ENV
                  fi
                  source $BASH_ENV
          dockerfile: Dockerfile.offline
          requires:
            - build-and-test
          filters:
            branches:
              only:
                - master
      ##### Staging ######
      - update-ecs-task-staging-migration:
          requires:
            - aws-ecr/build-and-push-image
          filters:
            branches:
              only:
                - staging
      - migration-staging:
          requires:
            - update-ecs-task-staging-migration
      - update-ecs-task-staging:
          requires:
            - aws-ecr/build-and-push-image
            - migration-staging
          filters:
            branches:
              only:
                - staging
      - update-ecs-service-staging:
          requires:
            - update-ecs-task-staging
      ##### Prod #####
      - update-ecs-task-prod-migration:
          requires:
            - aws-ecr/build-and-push-image
          filters:
            branches:
              only:
                - master
      - migration-prod:
          requires:
            - update-ecs-task-prod-migration
      - update-ecs-task-prod:
          requires:
            - migration-prod
            - aws-ecr/build-and-push-image
          filters:
            branches:
              only:
                - master
      - update-ecs-service-prod:
          requires:
            - update-ecs-task-prod
