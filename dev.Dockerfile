FROM node:18-alpine
RUN apk update && apk add --no-cache python3 make g++ py3-setuptools curl-dev \
    && ln -sf /usr/bin/python3 /usr/bin/python
RUN mkdir -p /home/<USER>/app/node_modules && chown -R node:node /home/<USER>/app
WORKDIR /home/<USER>/app
COPY package.json yarn.lock ./
ENV PYTHON=/usr/bin/python
RUN yarn install --ignore-engines
USER node
COPY --chown=node:node . .
ENV NODE_OPTIONS=--max-old-space-size=4096
RUN yarn build
EXPOSE 3200
CMD ["yarn", "start:prod"]
