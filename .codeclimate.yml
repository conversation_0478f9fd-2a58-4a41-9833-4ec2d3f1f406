version: "2"
checks:
  argument-count:
    config:
      threshold: 8
  complex-logic:
    config:
      threshold: 10
  file-lines:
    config:
      threshold: 1000
  method-complexity:
    config:
      threshold: 10
  method-count:
    config:
      threshold: 20
  method-lines:
    config:
      threshold: 50
  nested-control-flow:
    config:
      threshold: 4
  return-statements:
    config:
      threshold: 4
  similar-code:
    enabled: false
    config:
      threshold: # language-specific defaults. an override will affect all languages.
  identical-code:
    enabled: true
    config:
      threshold: # language-specific defaults. an override will affect all languages.
plugins:
  tslint:
    enabled: true
  config:
    config: tsconfig.json
    extensions:
    - .js
    - .ts
  fixme:
    enabled: true
    config:
      strings:
      - FIXME
      - BUG
      - TODO
ratings:
  paths:
    - "src/**/*.ts"
exclude_patterns:
  - ".circleci/"
  - "__mocks__/"
  - "./jest"
  - "src/**/*.spec.ts"
  - "src/**/*.model.ts"
  - "src/**/*.entity.ts"
  - "node_modules/"
  - "./src/database/migrations/"
  - "README.md"