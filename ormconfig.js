/**
 * @description this file configures typeorm connection on startup
 */
const dotenv = require('dotenv');

process.env.ENV_PATH
  ? dotenv.config({ path: process.env.ENV_PATH })
  : dotenv.config();

const env = process.env.NODE_ENV;
const allowReplica = ['production', 'staging'];
const REPLICAS_ENV = process.env.PG_REPLICAS;
const replicas = REPLICAS_ENV?.split(',') || [];

module.exports = {
  type: 'postgres',
  entities: ['dist/**/*.model.js'],
  subscribers: ['dist/**/*.subscriber.js'],
  logging: process.env.DATABASE_LOGGING,
  synchronize: false,
  migrations: ['dist/database/migrations/*.js'],
  cli: {
    migrationsDir: 'src/database/migrations',
  },
  replication: {
    master: {
      host: process.env.PGHOST,
      port: Number.parseInt(process.env.PGPORT, 2),
      username: process.env.PGUSER,
      password: process.env.PGPASSWORD,
      database: `${process.env.PGDATABASE}${
        process.env.NODE_ENV == 'test' ? '_test' : ''
      }`,
    },
    slaves: allowReplica.includes(env)
      ? replicas.map((replica) => ({
          host: replica,
          port: Number.parseInt(process.env.PGPORT, 2),
          username: process.env.PGUSER,
          password: process.env.PGPASSWORD,
          database: `${process.env.PGDATABASE}${
            process.env.NODE_ENV == 'test' ? '_test' : ''
          }`,
        }))
      : [],
  },
};
