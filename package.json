{"name": "api", "version": "0.0.1", "description": "", "author": "", "engines": {"node": ">=17.1.x <19.0.0", "npm": ">=6.0.0"}, "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "migration:create": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli migration:create src/database/migrations/add-name", "migration:run": "yarn run typeorm -- migration:run", "migration:revert": "yarn run typeorm -- migration:revert", "migrate": "run-s build && run-s migration:run", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli -d src/data-source.ts", "migrate:down": "run-s build && run-s migration:revert", "gen-migration": "yarn run typeorm -- migration:generate src/database/migrations/add-name", "fix": "run-s fix:*", "fix:prettier": "prettier --write 'src/**/*.{ts,js,json}'", "fix:eslint": "eslint 'src/**/*.{js,ts,tsx}' --ext .js,.ts --fix", "lint": "NODE_OPTIONS=--max-old-space-size=12288 run-s lint:*", "lint:prettier": "prettier -c 'src/**/*.{ts,js,json}'", "lint:eslint": "eslint 'src/**/*.{js,ts,tsx}' --ext .js,.ts", "test": "NODE_OPTIONS=--max-old-space-size=12288 NODE_ENV=test yarn migrate && NODE_ENV=test jest", "test:coverage": "NODE_ENV=test yarn migrate && NODE_ENV=test jest --coverage", "test:unit": "NODE_ENV=test jest", "codecov": "./node_modules/.bin/codecov -f coverage/coverage-final.json"}, "dependencies": {"@akumzy/sof-strategy": "^1.2.5", "@aws-sdk/client-bedrock-runtime": "^3.812.0", "@aws-sdk/client-cloudwatch-logs": "3.58.0", "@aws-sdk/client-s3": "^3.400.0", "@aws-sdk/client-transcribe": "^3.741.0", "@aws-sdk/client-transcribe-streaming": "^3.741.0", "@aws-sdk/s3-request-presigner": "^3.400.0", "@casl/ability": "4.1.6", "@clinify19/node-fhir-server-core": "^2.3.13", "@golevelup/nestjs-discovery": "4.0.0", "@lexical/headless": "^0.14.2", "@lexical/link": "^0.14.2", "@lexical/list": "^0.14.2", "@lexical/react": "^0.14.2", "@lexical/rich-text": "^0.14.2", "@lexical/table": "^0.14.2", "@lexical/utils": "^0.14.2", "@nestjs/apollo": "10.0.19", "@nestjs/axios": "0.1.0", "@nestjs/bull": "0.6.1", "@nestjs/common": "9.0.5", "@nestjs/core": "9.0.5", "@nestjs/event-emitter": "1.3.0", "@nestjs/graphql": "10.0.21", "@nestjs/jwt": "8.0.0", "@nestjs/mongoose": "^10.0.1", "@nestjs/passport": "8.0.1", "@nestjs/platform-express": "9.0.11", "@nestjs/platform-socket.io": "9.0.5", "@nestjs/schedule": "1.0.2", "@nestjs/swagger": "5.0.9", "@nestjs/typeorm": "9.0.0", "@nestjs/websockets": "9.0.5", "@socket.io/redis-adapter": "7.2.0", "@willsoto/nestjs-prometheus": "5.1.2", "apollo-server-express": "3.10.0", "apollo-server-plugin-base": "^3.7.2", "aws-sdk": "2.823.0", "bcryptjs": "2.4.3", "bluebird": "3.7.2", "bull": "4.9.0", "chance": "1.1.8", "class-transformer": "0.5.1", "class-validator": "0.13.2", "cloudinary": "1.30.1", "csv-parse": "^5.4.0", "csvtojson": "2.0.10", "dhis2-openapi": "^0.1.0", "dot-prop": "5.2.0", "escape-html": "1.0.3", "eslint-import-resolver-typescript": "2.3.0", "eslint-plugin-prettier": "3.1.4", "fast-xml-parser": "^5.2.5", "fhir": "^4.12.0", "googleapis": "105.0.0", "graphql": "16.5.0", "graphql-redis-subscriptions": "2.3.1", "graphql-tools": "6.2.0", "handlebars": "4.7.7", "html-to-pdfmake": "2.5.1", "ioredis": "5.4.2", "joi": "17.6.0", "jsdom": "22.1.0", "jsonwebtoken": "^9.0.2", "katex": "^0.16.9", "lexical": "^0.14.2", "lodash.clonedeep": "4.5.0", "lodash.difference": "4.5.0", "lodash.groupby": "4.6.0", "lodash.isequal": "4.5.0", "mockdate": "3.0.5", "moment": "2.27.0", "mongodb": "3.6.6", "mongoose": "^7.4.2", "multer-s3": "3.0.1", "nanoid": "3.1.31", "nest-winston": "1.7.0", "node-libcurl": "^3.0.0", "nodemailer": "6.7.8", "npm-run-all": "4.1.5", "oauth": "0.10.0", "passport": "0.6.0", "passport-headerapikey": "^1.2.2", "passport-jwt": "4.0.0", "passport-local": "1.0.0", "pdfmake": "0.2.7", "pg": "8.7.3", "prom-client": "^14.2.0", "redis": "3.0.2", "reflect-metadata": "0.1.13", "rimraf": "3.0.2", "rxjs": "7.5.6", "secure-random-password": "0.2.3", "socket.io": "^4.7.2", "sqs-consumer": "7.0.1", "throng": "5.0.0", "to-words": "^4.0.1", "twilio": "^5.3.4", "type-graphql": "1.0.0", "typeorm": "0.3.7", "typeorm-transactional-cls-hooked": "0.1.21", "uuid": "8.3.2", "winston": "3.6.0", "winston-cloudwatch": "3.1.1"}, "devDependencies": {"@nestjs/cli": "9.1.3", "@nestjs/schematics": "7.1.1", "@nestjs/testing": "9.0.11", "@types/bluebird": "3.5.36", "@types/bull": "3.15.9", "@types/chance": "1.1.3", "@types/cron": "1.7.3", "@types/escape-html": "^1.0.4", "@types/express": "4.17.14", "@types/fhir": "^0.0.41", "@types/html-to-pdfmake": "^2.4.4", "@types/jest": "29.0.3", "@types/jsdom": "^21.1.6", "@types/lodash.groupby": "^4.6.9", "@types/mongodb": "^4.0.7", "@types/multer": "1.4.7", "@types/node": "18.7.3", "@types/passport-jwt": "3.0.6", "@types/passport-local": "1.0.33", "@types/pdfmake": "0.2.2", "@types/redis": "4.0.11", "@types/rosie": "0.0.40", "@types/secure-random-password": "0.2.0", "@types/supertest": "2.0.12", "@types/throng": "5.0.4", "@typescript-eslint/eslint-plugin": "5.38.0", "@typescript-eslint/parser": "5.38.0", "codecov": "3.8.3", "dotenv": "16.0.1", "eslint": "8.23.1", "eslint-config-prettier": "6.11.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsdoc": "39.3.6", "eslint-plugin-prefer-arrow": "1.2.3", "husky": "8.0.1", "jest": "26.4.2", "lint-staged": "13.0.3", "prettier": "2.7.1", "redis-mock": "0.56.3", "rosie": "2.1.0", "supertest": "6.2.4", "ts-jest": "26.4.2", "ts-loader": "9.4.1", "ts-node": "10.9.1", "tsconfig-paths": "4.1.0", "typescript": "4.7.4"}, "jest": {"rootDir": "src", "testEnvironment": "node", "testRegex": ".spec.ts$", "preset": "ts-jest", "setupFiles": ["<rootDir>/../jest/reflect-metadata.js", "<rootDir>/../jest/load-config.js", "<rootDir>/../jest/global-mocks.js"], "moduleNameMapper": {"^@clinify/(.*)$": "<rootDir>/$1", "^@mocks/(.*)$": "<rootDir>/__mocks__/$1", "^@fixtures/(.*)$": "<rootDir>/utils/tests/$1"}, "coveragePathIgnorePatterns": ["/node_modules/", "/.node/", "/jest/", "src/database/", "src/config", "src/__mocks__/", "src/utils/tests/", "src/shared/lexical/nodes/"], "coverageDirectory": "<rootDir>/../coverage", "setupFilesAfterEnv": ["<rootDir>/../jest/redis-mock.js", "<rootDir>/../jest/jest.setup.js"], "coverageThreshold": {"global": {"statements": 92, "branches": 65, "functions": 94, "lines": 93}}}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"**/*.{ts,js,md,json}": "prettier --write"}, "volta": {"node": "18.20.8", "yarn": "1.22.22"}, "resolutions": {"wrap-ansi": "7.0.0", "string-width": "4.1.0"}}