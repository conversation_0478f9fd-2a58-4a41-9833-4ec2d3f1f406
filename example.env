NODE_ENV=
DATABASE_LOGGING=
APP_PORT=
SECRET=
RINGCAPTCHA_APP_KEY=
RINGCAPTCHA_API_KEY=
PAYSTACK_BASEURL=
PAYSTACK_SECRET_KEY=
TRANSACTION_REFERENCE_SALT=
MINIMUM_WITHDRAWABLE_AMOUNT=
AUTO_APPROVE_WITHDRAWALS=
FEEDBACK_SHEET_ID=
FEEDBACK_CREDENTIALS_KEY=
FEEDBACK_CREDENTIALS_EMAIL=
SEND_GRID_USERNAME=
SEND_GRID_PWD=
LEADWAY_BASE_URL=
DATABASE_LOGGING=
INTEGRATION_TOKEN=
ICD_CLIENT_ID=
ICD_CLIENT_SECRET=
ICD_TOKEN_URL=
DRUG_BANK_TOKEN=
PERCENTAGE_TRANSACTION_CHARGE=
PAYMENT_QUEUE_URL=
PAYMENT_DEAD_LETTER_QUEUE_URL=
CANCELLATION_LEVY_PERCENT=
CANCELLATION_LEVY_CLINIFY_COMISSION=
CLOUDINARY_API_KEY=
CLOUDINARY_API_SECRET=
CLOUDINARY_CLOUD_NAME=
CLOUDINARY_URL=
WEB_CONCURRENCY=
MEET_BASE_URL=
MEET_TOKEN_SECRET=
## aws
AWS_ACCESS_KEY=
AWS_KEY_SECRET=
REDIS_URL=
MONGO_URL=
### DATABASE SETTING
PGDATABASE=
PGPORT=
PGPASSWORD=
PGHOST=
PGUSER=
REPLICAS=
