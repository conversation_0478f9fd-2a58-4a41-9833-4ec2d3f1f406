version: '3'
services:
  clinify:
    image: clinify-api
    container_name: clinify
    ports:
      - 80:3200
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
      - migration
  migration:
    image: clinify-api
    container_name: migrations
    command: ["yarn", "migration:run"]
    env_file:
      - .env
    depends_on:
      - postgres
  postgres:
    image: postgres
    restart: always
    container_name: postgres
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: ${PGUSER}
      POSTGRES_PASSWORD: ${PGPASSWORD}
      POSTGRES_DB: ${PGDATABASE}
    volumes:
      - postgres:/var/lib/postgresql/data
  redis:
    image: redis
    container_name: redis
    ports:
      - 6379:6379
volumes:
  postgres:
