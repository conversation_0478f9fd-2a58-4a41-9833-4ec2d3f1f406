name: <PERSON><PERSON> and Push Docker Images

on:
  workflow_call:
    inputs:
      repository:
        required: true
        type: string
      image_tag:
        description: Custom image tag
        required: false
        type: string
      environment:
        description: 'environment name'
        required: false
        type: string
      docker_path:
        required: false
        default: .
        type: string
      docker_file_path:
        required: false
        default: ./dev.Dockerfile
        type: string
        description: --file argument to pass for docker build
      artifact_name:
        required: false
        type: string
        description: Name of the artifact to download
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      BUILD_ARGS:
        required: false
    outputs:
      ecrRegistry:
        description: The ECR registry where the image is uploaded
        value: ${{ jobs.build.outputs.ecrRegistry }}
      imageTag:
        description: The image tag
        value: ${{ jobs.build.outputs.imageTag }}
      tag:
        description: The image tag
        value: ${{ jobs.build.outputs.outcome }}

jobs:
  build:
    outputs:
      outcome: ${{ steps.set-tag.outputs.image-tag }}
      ecrRegistry: ${{ steps.login-ecr.outputs.registry }}
      imageTag: ${{ steps.set-tag.outputs.imageTag }}
    name: Build and Publish Image
    runs-on: github-runner-dev
    container:
      image: cimg/node:18.20.8
      options: --user root

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Download artifact
        if: ${{ inputs.artifact_name }}
        uses: actions/download-artifact@v4
        with:
          name: ${{ inputs.artifact_name }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1
          mask-aws-account-id: false

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Install Build Tools
        run: |
          sudo apt-get update
          sudo apt-get install -y python3 make g++
      - name: Build and Tag
        uses: docker/build-push-action@v2
        with:
          context: ${{ inputs.docker_path }}
          file: ${{ inputs.docker_file_path }}
          push: true
          tags: ${{ steps.login-ecr.outputs.registry }}/${{ inputs.repository }}:${{ inputs.image_tag || inputs.environment && format('{0}-{1}', inputs.environment, github.sha) || github.sha }}
          build-args: ${{ secrets.BUILD_ARGS }}
          load: false
          platforms: linux/amd64,linux/arm64

      - name: Output image tag
        id: set-tag
        run: |
          image_tag="${{ inputs.image_tag || inputs.environment && format('{0}-{1}', inputs.environment, github.sha) || github.sha }}"
          echo "image-tag=${image_tag}" >> $GITHUB_ENV
          echo "imageTag=${image_tag}" >> $GITHUB_ENV
          echo "image-tag=${image_tag}" >> $GITHUB_OUTPUT
          echo "imageTag=${image_tag}" >> $GITHUB_OUTPUT
