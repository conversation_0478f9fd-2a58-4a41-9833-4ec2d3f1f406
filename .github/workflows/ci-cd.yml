name: CI/CD
on:
  push:
    branches:
      - 'develop'
jobs:
  determine-environment:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.set_env.outputs.environment }}
    steps:
      - name: Set Environment Name
        id: set_env
        run: |
          if [[ $GITHUB_REF == 'refs/heads/develop' ]]; then
            echo "environment=develop" >> $GITHUB_OUTPUT
          elif [[ $GITHUB_REF == 'refs/heads/main' ]]; then
            echo "environment=prod" >> $GITHUB_OUTPUT
          elif [[ $GITHUB_REF == 'refs/heads/uat' ]]; then
            echo "environment=uat" >> $GITHUB_OUTPUT
          else
            echo "environment=development" >> $GITHUB_OUTPUT
          fi
          cat $GITHUB_OUTPUT
  build_and_test:
    needs: determine-environment
    runs-on: github-runner-dev
    container:
      image: cimg/node:18.20.8
      options: --user root
    services:
      postgres:
        image: postgres:14
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
        env:
          POSTGRES_DB: postgres_test
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
    env:
      NODE_ENV: test
      SECRET: ${{ secrets.SECRET }}
      PGHOST: postgres
      PGUSER: postgres
      PGPASSWORD: postgres
      PGDATABASE: postgres
      PGPORT: 5432
      SEND_GRID_USERNAME: ${{ secrets.SEND_GRID_USERNAME }}
      SEND_GRID_PWD: ${{ secrets.SEND_GRID_PWD }}
      INTEGRATION_TOKEN: ${{ secrets.INTEGRATION_TOKEN }}
      RINGCAPTCHA_APP_KEY: ${{ secrets.RINGCAPTCHA_APP_KEY }}
      PAYSTACK_BASEURL: ${{ secrets.PAYSTACK_BASEURL }}
      PAYSTACK_SECRET_KEY: ${{ secrets.PAYSTACK_SECRET_KEY }}
      PAYMENT_QUEUE_URL: ${{ secrets.PAYMENT_QUEUE_URL }}
      PAYMENT_DEAD_LETTER_QUEUE_URL: ${{ secrets.PAYMENT_DEAD_LETTER_QUEUE_URL }}
      FEEDBACK_SHEET_ID: ${{ secrets.FEEDBACK_SHEET_ID }}
      FEEDBACK_CREDENTIALS_KEY: ${{ secrets.FEEDBACK_CREDENTIALS_KEY }}
      FEEDBACK_CREDENTIALS_EMAIL: ${{ secrets.FEEDBACK_CREDENTIALS_EMAIL }}
      TRANSACTION_REFERENCE_SALT: ${{ secrets.TRANSACTION_REFERENCE_SALT }}
      ICD_CLIENT_ID: ${{ secrets.ICD_CLIENT_ID }}
      ICD_CLIENT_SECRET: ${{ secrets.ICD_CLIENT_SECRET }}
      ICD_TOKEN_URL: ${{ secrets.ICD_TOKEN_URL }}
      DRUG_BANK_TOKEN: ${{ secrets.DRUG_BANK_TOKEN }}
      MINIMUM_WITHDRAWABLE_AMOUNT: ${{ secrets.MINIMUM_WITHDRAWABLE_AMOUNT }}
      PERCENTAGE_TRANSACTION_CHARGE: ${{ secrets.PERCENTAGE_TRANSACTION_CHARGE }}
      CANCELLATION_LEVY_PERCENT: ${{ secrets.CANCELLATION_LEVY_PERCENT }}
      CANCELLATION_LEVY_CLINIFY_COMISSION: ${{ secrets.CANCELLATION_LEVY_CLINIFY_COMISSION }}
      REDIS_URL: ${{ secrets.REDIS_URL }}
      REDIS_HOST: ${{ secrets.REDIS_HOST }}
      MEET_BASE_URL: ${{ secrets.MEET_BASE_URL }}
      MEET_TOKEN_SECRET: ${{ secrets.MEET_TOKEN_SECRET }}
      AWS_ACCESS_KEY: ${{ secrets.AWS_ACCESS_KEY }}
      AWS_KEY_SECRET: ${{ secrets.AWS_KEY_SECRET }}
    steps:
      - name: Set up workspace permissions
        run: sudo chmod -R 777 /__w
      - name: Set up specific directory permissions
        run: sudo chmod -R 777 /__w/_temp/_runner_file_commands
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Restore YARN Package Cache
        uses: actions/cache@v3
        with:
          path: node_modules
          key: v2-dependencies-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            v2-dependencies-
      - name: Install dependencies
        run: yarn install --ignore-engines
      - name: Linting
        run: yarn lint
      - name: Run Tests and Collect Coverage Reports
        run: |
          mkdir -p test-results/jest
          yarn test --maxWorkers=2 --coverage
      - name: Store Coverage Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: coverage
          path: coverage
      - name: Save YARN Package Cache
        uses: actions/cache@v3
        with:
          path: node_modules
          key: v2-dependencies-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            v2-dependencies-
  build_push_ecr:
    needs: build_and_test
    if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/main' || github.ref == 'refs/heads/uat'
    uses: ./.github/workflows/build_push_image.yaml
    with:
      repository: clinify-api-dev
      environment: ${{ needs.determine-environment.outputs.environment }}
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  run-migrations:
    needs: build_push_ecr
    runs-on: github-runner-dev
    container:
      image: cimg/node:18.20.8
      options: --user root
    env:
      NODE_ENV: dev
      SECRET: ${{ secrets.SECRET }}
      PGHOST: ${{ secrets.PGHOST }}
      PGUSER: ${{ secrets.PGUSER }}
      PGPASSWORD: ${{ secrets.PGPASSWORD }}
      PGDATABASE: ${{ secrets.PGDATABASE }}
      PGSSLMODE: ${{ secrets.PGSSLMODE }}
      PGPORT: ${{ secrets.PGPORT }}
      SEND_GRID_USERNAME: ${{ secrets.SEND_GRID_USERNAME }}
      SEND_GRID_PWD: ${{ secrets.SEND_GRID_PWD }}
      INTEGRATION_TOKEN: ${{ secrets.INTEGRATION_TOKEN }}
      RINGCAPTCHA_APP_KEY: ${{ secrets.RINGCAPTCHA_APP_KEY }}
      PAYSTACK_BASEURL: ${{ secrets.PAYSTACK_BASEURL }}
      PAYSTACK_SECRET_KEY: ${{ secrets.PAYSTACK_SECRET_KEY }}
      PAYMENT_QUEUE_URL: ${{ secrets.PAYMENT_QUEUE_URL }}
      PAYMENT_DEAD_LETTER_QUEUE_URL: ${{ secrets.PAYMENT_DEAD_LETTER_QUEUE_URL }}
      FEEDBACK_SHEET_ID: ${{ secrets.FEEDBACK_SHEET_ID }}
      FEEDBACK_CREDENTIALS_KEY: ${{ secrets.FEEDBACK_CREDENTIALS_KEY }}
      FEEDBACK_CREDENTIALS_EMAIL: ${{ secrets.FEEDBACK_CREDENTIALS_EMAIL }}
      TRANSACTION_REFERENCE_SALT: ${{ secrets.TRANSACTION_REFERENCE_SALT }}
      ICD_CLIENT_ID: ${{ secrets.ICD_CLIENT_ID }}
      ICD_CLIENT_SECRET: ${{ secrets.ICD_CLIENT_SECRET }}
      ICD_TOKEN_URL: ${{ secrets.ICD_TOKEN_URL }}
      DRUG_BANK_TOKEN: ${{ secrets.DRUG_BANK_TOKEN }}
      MINIMUM_WITHDRAWABLE_AMOUNT: ${{ secrets.MINIMUM_WITHDRAWABLE_AMOUNT }}
      PERCENTAGE_TRANSACTION_CHARGE: ${{ secrets.PERCENTAGE_TRANSACTION_CHARGE }}
      CANCELLATION_LEVY_PERCENT: ${{ secrets.CANCELLATION_LEVY_PERCENT }}
      CANCELLATION_LEVY_CLINIFY_COMISSION: ${{ secrets.CANCELLATION_LEVY_CLINIFY_COMISSION }}
      REDIS_URL: ${{ secrets.REDIS_URL }}
      REDIS_HOST: ${{ secrets.REDIS_HOST }}
      MEET_BASE_URL: ${{ secrets.MEET_BASE_URL }}
      MEET_TOKEN_SECRET: ${{ secrets.MEET_TOKEN_SECRET }}
      AWS_ACCESS_KEY: ${{ secrets.AWS_ACCESS_KEY }}
      AWS_KEY_SECRET: ${{ secrets.AWS_KEY_SECRET }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'  # Compatible with ">=17.1.x <19.0.0"
      - name: Install dependencies
        run: yarn install --ignore-engines
      - name: Build the project
        run: yarn build
      - name: Run Migrations
        run: yarn migrate
      - name: Verify Migration
        run: |
          if [ $? -ne 0 ]; then
            echo "Migration failed."
            exit 1
          else
            echo "Migration completed successfully."
          fi
  deploy:
    needs:
      - run-migrations
      - build_push_ecr
      - determine-environment
    name: Deploying reverse ip to ${{ needs.determine-environment.outputs.environment }}
    if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/main' || github.ref == 'refs/heads/uat'
    uses: ./.github/workflows/deploy_argocd.yaml
    with:
      image-tag: ${{ needs.build_push_ecr.outputs.tag }}
      ecr_repository: clinify-api-dev
      file_path: ${{ needs.determine-environment.outputs.environment }}/clinify-api-dev.yaml
    secrets:
      GITOPS_TOKEN: ${{ secrets.GITOPS_TOKEN }}
